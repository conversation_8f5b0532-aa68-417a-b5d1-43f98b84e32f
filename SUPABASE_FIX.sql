-- Fix for existing Supabase tables
-- Run this to ensure proper configuration

-- First, let's check what's wrong with the tenants table
-- and fix the UUID generation

-- Make sure UUID extension is enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Check if the tenants table has the right default for id
-- If not, alter it
DO $$
BEGIN
    -- Update the default value for id column if it's not set correctly
    ALTER TABLE public.tenants ALTER COLUMN id SET DEFAULT uuid_generate_v4();
EXCEPTION 
    WHEN OTHERS THEN 
        -- If there's an error, the default might already be set
        NULL;
END $$;

-- Same for user_tenants table
DO $$
BEGIN
    ALTER TABLE public.user_tenants ALTER COLUMN id SET DEFAULT uuid_generate_v4();
EXCEPTION 
    WHEN OTHERS THEN 
        NULL;
END $$;

-- Ensure RLS policies exist for tenant creation
DROP POLICY IF EXISTS "Allow tenant creation" ON public.tenants;
CREATE POLICY "Allow tenant creation" ON public.tenants
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Allow user profile creation" ON public.users;
CREATE POLICY "Allow user profile creation" ON public.users
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Allow user-tenant associations" ON public.user_tenants;
CREATE POLICY "Allow user-tenant associations" ON public.user_tenants
    FOR INSERT WITH CHECK (true);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.tenants TO anon, authenticated;
GRANT ALL ON public.users TO anon, authenticated;
GRANT ALL ON public.user_tenants TO anon, authenticated;