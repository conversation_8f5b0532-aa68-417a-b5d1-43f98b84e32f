# Next.js + Supabase Boilerplate Environment Variables
# Copy this file to .env.local and fill in your actual values

# =====================================================
# SUPABASE CONFIGURATION
# =====================================================

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Your Supabase service role key (keep this secret!)
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =====================================================
# DATABASE CONFIGURATION
# =====================================================

# Database connection string for Prisma/direct connections
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================

# Your application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment
NODE_ENV=development

# =====================================================
# OPTIONAL: AUTHENTICATION PROVIDERS
# =====================================================

# Google OAuth (if using Google authentication)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth (if using GitHub authentication)
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret

# =====================================================
# OPTIONAL: EMAIL CONFIGURATION
# =====================================================

# SMTP settings for email notifications (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# =====================================================
# DEVELOPMENT SETTINGS
# =====================================================

# Enable debug logging (optional)
# DEBUG=true