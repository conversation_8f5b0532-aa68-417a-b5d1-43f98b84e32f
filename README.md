# Next.js + Supabase Boilerplate

A clean, minimal boilerplate for building modern web applications with Next.js 14 and Supabase.

## Features

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Supabase** for backend services (Database, Auth, Real-time)
- **Tailwind CSS** for styling
- **Multi-tenant ready** architecture
- **Authentication** with Supabase Auth
- **Database** with PostgreSQL and Row Level Security
- **Modern UI components** with shadcn/ui

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL with Row Level Security
- **Styling**: Tailwind CSS
- **Components**: shadcn/ui

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
```bash
git clone <your-repo-url>
cd your-project-name
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
Copy the environment file and configure your values:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database
DATABASE_URL=your-database-connection-string

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **Set up the database**
- Go to your Supabase dashboard
- Navigate to the SQL Editor
- Run the schema files in the `prisma/` directory to set up your tables

5. **Run the development server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see your application.

## Project Structure

```
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── api/            # API routes
│   │   ├── auth/           # Authentication pages
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Root layout
│   │   └── page.tsx        # Home page
│   ├── components/         # Reusable UI components
│   │   ├── auth/          # Authentication components
│   │   ├── layout/        # Layout components
│   │   └── ui/            # Base UI components
│   ├── contexts/          # React contexts
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   │   ├── supabase/      # Supabase client configuration
│   │   └── validations/   # Form validation schemas
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── public/                # Static assets
├── prisma/               # Database schema
└── package.json          # Dependencies and scripts
```

## Key Features

### Authentication
- Built-in authentication with Supabase Auth
- Sign up, sign in, and password reset flows
- Protected routes and middleware
- User session management

### Database
- PostgreSQL database with Supabase
- Type-safe database queries
- Row Level Security (RLS) for data protection
- Real-time subscriptions

### Multi-tenant Architecture
- Tenant context and management
- Data isolation between tenants
- Tenant-specific routing and middleware

### Modern UI
- Responsive design with Tailwind CSS
- Dark mode support
- Accessible components
- Clean, modern interface

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

## Environment Variables

Create a `.env.local` file with the following variables:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Database
DATABASE_URL=

# App
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push to main branch

### Other Platforms
This boilerplate can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License
