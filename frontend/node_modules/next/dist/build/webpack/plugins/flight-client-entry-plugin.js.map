{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "names": ["FlightClientEntryPlugin", "PLUGIN_NAME", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "actionModServerId", "actionModEdgeServerId", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "path", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "constructor", "options", "dev", "appDir", "isEdgeServer", "serverActionsBodySizeLimit", "assetPrefix", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "resource", "layer", "WEBPACK_LAYERS", "serverSideRendering", "ssrNamedModuleId", "relative", "context", "startsWith", "normalizePathSep", "replace", "traverseModules", "_chunk", "_chunkGroup", "request", "buildInfo", "rsc", "moduleGraph", "isAsync", "String", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "forEachEntryModule", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "getOutgoingConnections", "entryRequest", "dependency", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "names", "isAbsoluteRequest", "isAbsolute", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "APP_CLIENT_INTERNALS", "size", "createdActions", "actionNames", "actionName", "injectActionEntry", "actions", "finishModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDepdendencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "id", "fromClient", "Promise", "all", "invalidator", "getInvalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "COMPILER_NAMES", "client", "map", "addClientEntryAndSSRModules", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "modRequest", "BARREL_OPTIMIZATION_PREFIX", "getActions", "entryDependency", "ssrEntryModule", "getResolvedModule", "visited", "CSSImports", "filterClientComponents", "isCSS", "isCSSMod", "_identifier", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "EDGE_RUNTIME_WEBPACK", "isClientComponentEntryModule", "Array", "from", "loaderOptions", "modules", "regexCSS", "test", "localeCompare", "server", "clientLoader", "stringify", "importPath", "sep", "clientSSRLoader", "getEntries", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentEntryDep", "EntryPlugin", "createDependency", "addEntry", "actionsArray", "semver", "lt", "process", "version", "errors", "WebpackError", "resolve", "actionLoader", "JSON", "__client_imported__", "currentCompilerServerActions", "p", "generateActionId", "workers", "<PERSON><PERSON><PERSON><PERSON>", "reactServerComponents", "actionEntryDep", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "action", "json", "node", "edge", "<PERSON><PERSON><PERSON>", "generateRandomActionKeyRaw", "undefined", "SERVER_REFERENCE_MANIFEST", "sources", "RawSource"], "mappings": ";;;;+BA8JaA;;;eAAAA;;;yBAxJW;6BACE;6DACT;sCAOV;2BACwB;4BAOxB;uBAOA;wBAC6C;kCACnB;8BACK;+DAEnB;uCACwB;;;;;;AAS3C,MAAMC,cAAc;AAqBpB,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,mBAAmB,CAAC;IAOpBC,uBAAuB,CAAC;IAQxB,gEAAgE;IAChEC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IAEtB,6DAA6D;IAC7D,wEAAwE;IACxE,qFAAqF;IACrFC,sBAAsB,EAAE;IAExBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQC,aAAI,CAACC,KAAK,CAACR,OAAOS,IAAI;QACpC,MAAMC,QAAQH,aAAI,CAACC,KAAK,CAACP,OAAOQ,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACN;QAC9C,MAAMO,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIxB,iBAAkB;QACtD,KAAK,MAAMyB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWd,aAAI,CAACC,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEO,MAAMlC;IAOX6C,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,0BAA0B,GAAGJ,QAAQI,0BAA0B;QACpE,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;IAC/D;IAEAG,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BvD,aACA,CAACsD,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFZ,SAASC,KAAK,CAACY,UAAU,CAACC,UAAU,CAAClE,aAAa,CAACsD,cACjD,IAAI,CAACa,mBAAmB,CAACf,UAAUE;QAGrCF,SAASC,KAAK,CAACe,YAAY,CAACb,GAAG,CAACvD,aAAa,CAACsD;YAC5C,MAAMe,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI;gBAClE,MAAMiD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,iFAAiF;gBACjF,MAAMC,cAAcL,UAAUA,UAAUG,WAAWJ,IAAIO,QAAQ;gBAE/D,IAAIP,IAAIQ,KAAK,KAAKC,yBAAc,CAACC,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOX,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIK,mBAAmBxD,aAAI,CAACyD,QAAQ,CAAC/B,SAASgC,OAAO,EAAEP;oBAEvD,IAAI,CAACK,iBAAiBG,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BH,mBAAmB,CAAC,EAAE,EAAEI,IAAAA,kCAAgB,EAACJ,kBAAkB,CAAC;oBAC9D;oBAEA,IAAI,IAAI,CAAClC,YAAY,EAAE;wBACrB/C,YAAYO,mBAAmB,CAC7B0E,iBAAiBK,OAAO,CAAC,uBAAuB,eACjD,GAAGjB;oBACN,OAAO;wBACLrE,YAAYM,eAAe,CAAC2E,iBAAiB,GAAGZ;oBAClD;gBACF;YACF;YAEAkB,IAAAA,uBAAe,EAAClC,aAAa,CAACiB,KAAKkB,QAAQC,aAAapB;gBACtD,yFAAyF;gBACzF,4EAA4E;gBAC5E,IAAIC,IAAIoB,OAAO,IAAIpB,IAAIO,QAAQ,IAAI,CAACP,IAAIqB,SAAS,CAACC,GAAG,EAAE;oBACrD,IAAIvC,YAAYwC,WAAW,CAACC,OAAO,CAACxB,MAAM;wBACxCtE,YAAYQ,oBAAoB,CAACkC,IAAI,CAAC4B,IAAIO,QAAQ;oBACpD;gBACF;gBAEAT,aAAa2B,OAAO1B,QAAQC;YAC9B;QACF;QAEAnB,SAASC,KAAK,CAAC4C,IAAI,CAAC1C,GAAG,CAACvD,aAAa,CAACsD;YACpCA,YAAYD,KAAK,CAAC6C,aAAa,CAAChC,UAAU,CACxC;gBACEtC,MAAM5B;gBACNmG,OAAOxC,gBAAO,CAACyC,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAACjD,aAAagD;QAErD;IACF;IAEA,MAAMnC,oBAAoBf,QAA0B,EAAEE,WAAgB,EAAE;QACtE,MAAMkD,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAA4D,CAAC;QAEnE,4EAA4E;QAC5E,0BAA0B;QAC1BC,IAAAA,0BAAkB,EAACtD,aAAa,CAAC,EAAE1B,IAAI,EAAEiF,WAAW,EAAE;YACpD,MAAMC,sCAAsC,IAAI3E;YAGhD,MAAM4E,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAMrG,mBAA+B,CAAC;YAEtC,KAAK,MAAMsG,cAAc5D,YAAYwC,WAAW,CAACqB,sBAAsB,CACrEN,aACC;gBACD,uFAAuF;gBACvF,MAAMO,eAAeF,WAAWG,UAAU,CAAC1B,OAAO;gBAElD,MAAM,EAAE2B,sBAAsB,EAAEC,aAAa,EAAElF,UAAU,EAAE,GACzD,IAAI,CAACmF,6CAA6C,CAAC;oBACjDJ;oBACA9D;oBACAmE,gBAAgBP,WAAWO,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM,GACjCb,mBAAmBrD,GAAG,CAACiE,KAAKC;gBAG9B,MAAMC,oBAAoBnG,aAAI,CAACoG,UAAU,CAACV;gBAE1C,mDAAmD;gBACnD,IAAI,CAACS,mBAAmB;oBACtBP,uBAAuBI,OAAO,CAAC,CAACK,QAC9BjB,oCAAoCpE,GAAG,CAACqF;oBAE1C;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAMC,kBAAkBH,oBACpBnG,aAAI,CAACyD,QAAQ,CAAC7B,YAAYT,OAAO,CAACuC,OAAO,EAAEgC,gBAC3CA;gBAEJ,8CAA8C;gBAC9C,MAAMa,aAAa3C,IAAAA,kCAAgB,EACjC0C,gBAAgBzC,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlEzE,OAAOoH,MAAM,CAACtH,kBAAkByB;gBAChC4E,sBAAsBtE,IAAI,CAAC;oBACzBS;oBACAE;oBACAlB,WAAWR;oBACX0F;oBACAW;oBACAE,kBAAkBf;gBACpB;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAMnF,oBAAoBtB,8BAA8BC;YACxD,KAAK,MAAMwH,uBAAuBnB,sBAAuB;gBACvD,MAAMoB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;2BACVH,oBAAoBd,sBAAsB;2BACzCrF,iBAAiB,CAACmG,oBAAoBD,gBAAgB,CAAC,IAAI,EAAE;qBAClE;gBACH;gBAEA,2EAA2E;gBAC3E,IAAI,CAAC1B,8BAA8B,CAAC2B,oBAAoBhG,SAAS,CAAC,EAAE;oBAClEqE,8BAA8B,CAAC2B,oBAAoBhG,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACAqE,8BAA8B,CAAC2B,oBAAoBhG,SAAS,CAAC,CAACO,IAAI,CAChE0F,QAAQ,CAAC,EAAE;gBAGb7B,gCAAgC7D,IAAI,CAAC0F;YACvC;YAEA,sBAAsB;YACtB7B,gCAAgC7D,IAAI,CAClC,IAAI,CAAC2F,8BAA8B,CAAC;gBAClClF;gBACAE;gBACAlB,WAAWR;gBACX2G,eAAe;uBAAIzB;iBAAoC;gBACvDmB,YAAYO,gCAAoB;YAClC;YAGF,IAAIzB,mBAAmB0B,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAAC9B,kBAAkB,CAAC/E,KAAK,EAAE;oBAC7B+E,kBAAkB,CAAC/E,KAAK,GAAG,IAAIoF;gBACjC;gBACAL,kBAAkB,CAAC/E,KAAK,GAAG,IAAIoF,IAAI;uBAC9BL,kBAAkB,CAAC/E,KAAK;uBACxBmF;iBACJ;YACH;QACF;QAEA,MAAM2B,iBAAiB,IAAIvG;QAC3B,KAAK,MAAM,CAACP,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrD4F,oBACC;YACD,KAAK,MAAM,CAACgB,KAAKgB,YAAY,IAAI5B,mBAAoB;gBACnD,KAAK,MAAM6B,cAAcD,YAAa;oBACpCD,eAAehG,GAAG,CAACd,OAAO,MAAM+F,MAAM,MAAMiB;gBAC9C;YACF;YACAlC,mBAAmB/D,IAAI,CACrB,IAAI,CAACkG,iBAAiB,CAAC;gBACrBzF;gBACAE;gBACAwF,SAAS/B;gBACT3E,WAAWR;gBACXqG,YAAYrG;YACd;QAEJ;QAEA0B,YAAYD,KAAK,CAAC0F,aAAa,CAAC7E,UAAU,CAAClE,aAAa;YACtD,MAAMgJ,6BAA6C,EAAE;YACrD,MAAMC,2BAAkE,CAAC;YAEzE,mEAAmE;YACnE,gBAAgB;YAChB,yEAAyE;YACzE,KAAK,MAAM,CAACrH,MAAMsH,sBAAsB,IAAIpI,OAAOC,OAAO,CACxD0F,gCACC;gBACD,qEAAqE;gBACrE,qBAAqB;gBACrB,MAAMM,qBAAqB,IAAI,CAACoC,oCAAoC,CAAC;oBACnE7F;oBACAM,cAAcsF;gBAChB;gBAEA,IAAInC,mBAAmB0B,IAAI,GAAG,GAAG;oBAC/B,IAAI,CAACQ,wBAAwB,CAACrH,KAAK,EAAE;wBACnCqH,wBAAwB,CAACrH,KAAK,GAAG,IAAIoF;oBACvC;oBACAiC,wBAAwB,CAACrH,KAAK,GAAG,IAAIoF,IAAI;2BACpCiC,wBAAwB,CAACrH,KAAK;2BAC9BmF;qBACJ;gBACH;YACF;YAEA,KAAK,MAAM,CAACnF,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrDkI,0BACC;gBACD,uEAAuE;gBACvE,+CAA+C;gBAC/C,uEAAuE;gBACvE,mBAAmB;gBACnB,IAAIG,iCAAiC;gBACrC,MAAMC,8BAA8B,IAAIrC;gBACxC,KAAK,MAAM,CAACW,KAAKgB,YAAY,IAAI5B,mBAAoB;oBACnD,MAAMuC,uBAAuB,EAAE;oBAC/B,KAAK,MAAMV,cAAcD,YAAa;wBACpC,MAAMY,KAAK3H,OAAO,MAAM+F,MAAM,MAAMiB;wBACpC,IAAI,CAACF,eAAenG,GAAG,CAACgH,KAAK;4BAC3BD,qBAAqB3G,IAAI,CAACiG;wBAC5B;oBACF;oBACA,IAAIU,qBAAqB/H,MAAM,GAAG,GAAG;wBACnC8H,4BAA4B3F,GAAG,CAACiE,KAAK2B;wBACrCF,iCAAiC;oBACnC;gBACF;gBAEA,IAAIA,gCAAgC;oBAClCJ,2BAA2BrG,IAAI,CAC7B,IAAI,CAACkG,iBAAiB,CAAC;wBACrBzF;wBACAE;wBACAwF,SAASO;wBACTjH,WAAWR;wBACXqG,YAAYrG;wBACZ4H,YAAY;oBACd;gBAEJ;YACF;YAEA,OAAOC,QAAQC,GAAG,CAACV;QACrB;QAEA,qDAAqD;QACrD,MAAMW,cAAcC,IAAAA,oCAAc,EAACxG,SAASyG,UAAU;QACtD,4DAA4D;QAC5D,IACEF,eACAnD,gCAAgCsD,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAJ,YAAYK,UAAU,CAAC;gBAACC,0BAAc,CAACC,MAAM;aAAC;QAChD;QAEA,qGAAqG;QACrG,6EAA6E;QAC7E,MAAMT,QAAQC,GAAG,CACflD,gCAAgC2D,GAAG,CACjC,CAACC,8BAAgCA,2BAA2B,CAAC,EAAE;QAInE,uCAAuC;QACvC,MAAMX,QAAQC,GAAG,CAAChD;IACpB;IAEAyC,qCAAqC,EACnC7F,WAAW,EACXM,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMyG,mBAAmB,IAAIrD;QAE7B,gFAAgF;QAChF,MAAMsD,gBAAgB,IAAInI;QAC1B,MAAMoI,eAAe,IAAIpI;QAEzB,MAAMqI,iBAAiB,CAAC,EACtBpD,YAAY,EACZK,cAAc,EAIf;YACC,MAAMgD,sBAAsB,CAAClG;oBAOzBA,0BAAgCA,2BAM9BA;gBAZJ,IAAI,CAACA,KAAK;gBAEV,mEAAmE;gBACnE,yEAAyE;gBACzE,0EAA0E;gBAC1E,IAAImG,aACFnG,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI,MAAG6C,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK;gBAEhE,yEAAyE;gBACzE,yEAAyE;gBACzE,0EAA0E;gBAC1E,wEAAwE;gBACxE,KAAIL,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBc,UAAU,CAACsF,sCAA0B,GAAG;oBAC7DD,aAAanG,IAAIE,aAAa,GAAG,MAAMiG;gBACzC;gBAEA,IAAI,CAACA,cAAcJ,cAAc/H,GAAG,CAACmI,aAAa;gBAClDJ,cAAc5H,GAAG,CAACgI;gBAElB,MAAM5B,UAAU8B,IAAAA,iBAAU,EAACrG;gBAC3B,IAAIuE,SAAS;oBACXuB,iBAAiB3G,GAAG,CAACgH,YAAY5B;gBACnC;gBAEAxF,YAAYwC,WAAW,CACpBqB,sBAAsB,CAAC5C,KACvBmD,OAAO,CAAC,CAACR;oBACRuD,oBAAoBvD,WAAWO,cAAc;gBAC/C;YACJ;YAEA,yEAAyE;YACzE,IAAI,CAACL,aAAa3E,QAAQ,CAAC,oCAAoC;gBAC7D,2DAA2D;gBAC3DgI,oBAAoBhD;YACtB;QACF;QAEA,KAAK,MAAMoD,mBAAmBjH,aAAc;YAC1C,MAAMkH,iBACJxH,YAAYwC,WAAW,CAACiF,iBAAiB,CAACF;YAC5C,KAAK,MAAM3D,cAAc5D,YAAYwC,WAAW,CAACqB,sBAAsB,CACrE2D,gBACC;gBACD,MAAMzD,aAAaH,WAAWG,UAAU;gBACxC,MAAM1B,UAAU0B,WAAW1B,OAAO;gBAElC,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAI4E,aAAahI,GAAG,CAACoD,UAAU;gBAC/B4E,aAAa7H,GAAG,CAACiD;gBAEjB6E,eAAe;oBACbpD,cAAczB;oBACd8B,gBAAgBP,WAAWO,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO4C;IACT;IAEA7C,8CAA8C,EAC5CJ,YAAY,EACZ9D,WAAW,EACXmE,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMuD,UAAU,IAAI7I;QAEpB,mBAAmB;QACnB,MAAMmF,yBAAiD,EAAE;QACzD,MAAMC,gBAAsC,EAAE;QAC9C,MAAM0D,aAAa,IAAI9I;QAEvB,MAAM+I,yBAAyB,CAAC3G;gBAS5BA,0BAAgCA,2BAW9BA;YAnBJ,IAAI,CAACA,KAAK;YAEV,MAAM4G,QAAQC,IAAAA,eAAQ,EAAC7G;YAEvB,mEAAmE;YACnE,yEAAyE;YACzE,0EAA0E;YAC1E,IAAImG,aACFnG,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI,MAAG6C,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK;YAEhE,6EAA6E;YAC7E,IAAIL,IAAI3B,WAAW,CAAChB,IAAI,KAAK,iBAAiB;gBAC5C8I,aAAa,AAACnG,IAAY8G,WAAW;YACvC;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,wEAAwE;YACxE,KAAI9G,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBc,UAAU,CAACsF,sCAA0B,GAAG;gBAC7DD,aAAanG,IAAIE,aAAa,GAAG,MAAMiG;YACzC;YAEA,IAAI,CAACA,cAAcM,QAAQzI,GAAG,CAACmI,aAAa;YAC5CM,QAAQtI,GAAG,CAACgI;YAEZ,MAAM5B,UAAU8B,IAAAA,iBAAU,EAACrG;YAC3B,IAAIuE,SAAS;gBACXvB,cAAc5E,IAAI,CAAC;oBAAC+H;oBAAY5B;iBAAQ;YAC1C;YAEA,IAAIqC,OAAO;gBACT,MAAMG,iBACJ/G,IAAIgH,WAAW,IAAI,AAAChH,IAAIgH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAClI,YAAYwC,WAAW,CACpC2F,cAAc,CAAClH,KACfmH,YAAY,CACX,IAAI,CAAC1I,YAAY,GAAG2I,gCAAoB,GAAG;oBAG/C,IAAIH,QAAQ;gBACd;gBAEAP,WAAWvI,GAAG,CAACgI;YACjB;YAEA,IAAIkB,IAAAA,mCAA4B,EAACrH,MAAM;gBACrC+C,uBAAuB3E,IAAI,CAAC+H;gBAC5B;YACF;YAEApH,YAAYwC,WAAW,CACpBqB,sBAAsB,CAAC5C,KACvBmD,OAAO,CAAC,CAACR;gBACRgE,uBAAuBhE,WAAWO,cAAc;YAClD;QACJ;QAEA,2DAA2D;QAC3DyD,uBAAuBzD;QAEvB,OAAO;YACLH;YACAjF,YAAY4I,WAAWxC,IAAI,GACvB;gBACE,CAACrB,aAAa,EAAEyE,MAAMC,IAAI,CAACb;YAC7B,IACA,CAAC;YACL1D;QACF;IACF;IAEAe,+BAA+B,EAC7BlF,QAAQ,EACRE,WAAW,EACXlB,SAAS,EACTmG,aAAa,EACbN,UAAU,EACVE,gBAAgB,EAQjB,EAIC;QACA,IAAI4B,mBAAmB;QAEvB,MAAMgC,gBAAoD;YACxDC,SAASzD,cAAcvH,IAAI,CAAC,CAACC,GAAGC,IAC9B+K,eAAQ,CAACC,IAAI,CAAChL,KAAK,IAAID,EAAEkL,aAAa,CAACjL;YAEzCkL,QAAQ;QACV;QAEA,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,eAAe,CAAC,gCAAgC,EAAEC,IAAAA,sBAAS,EAAC;YAChEN,SAAS,IAAI,CAAChJ,YAAY,GACtB+I,cAAcC,OAAO,CAAC7B,GAAG,CAAC,CAACoC,aACzBA,WAAWhH,OAAO,CAChB,mCACA,cAAcA,OAAO,CAAC,OAAO7D,aAAI,CAAC8K,GAAG,MAGzCT,cAAcC,OAAO;YACzBI,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMK,kBAAkB,CAAC,gCAAgC,EAAEH,IAAAA,sBAAS,EAAC;YACnE,GAAGP,aAAa;YAChBK,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAACtJ,GAAG,EAAE;YACZ,MAAM/B,UAAU2L,IAAAA,gCAAU,EAACtJ,SAASyG,UAAU;YAC9C,MAAM8C,UAAUC,IAAAA,iCAAW,EAAC3C,0BAAc,CAACC,MAAM,EAAE,OAAOjC;YAE1D,IAAI,CAAClH,OAAO,CAAC4L,QAAQ,EAAE;gBACrB5L,OAAO,CAAC4L,QAAQ,GAAG;oBACjBE,MAAMC,gCAAU,CAACC,WAAW;oBAC5BC,eAAe,IAAI7K,IAAI;wBAACC;qBAAU;oBAClC6K,uBAAuB9E;oBACvBF;oBACAtC,SAAS0G;oBACTa,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACAtD,mBAAmB;YACrB,OAAO;gBACL,MAAMuD,YAAYvM,OAAO,CAAC4L,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIW,UAAU3H,OAAO,KAAK0G,cAAc;oBACtCiB,UAAU3H,OAAO,GAAG0G;oBACpBtC,mBAAmB;gBACrB;gBACA,IAAIuD,UAAUT,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAACtK,GAAG,CAACN;gBAC9B;gBACAkL,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLpN,YAAYS,qBAAqB,CAACuH,WAAW,GAAGoE;QAClD;QAEA,qDAAqD;QACrD,MAAMkB,0BAA0B5J,gBAAO,CAAC6J,WAAW,CAACC,gBAAgB,CAClEhB,iBACA;YACE7K,MAAMqG;QACR;QAGF,OAAO;YACL8B;YACA,6CAA6C;YAC7C,gGAAgG;YAChG,qEAAqE;YACrE,IAAI,CAAC2D,QAAQ,CACXpK,aACA,6BAA6B;YAC7BF,SAASgC,OAAO,EAChBmI,yBACA;gBACE,+BAA+B;gBAC/B3L,MAAMQ;gBACN,6CAA6C;gBAC7C,iEAAiE;gBACjE2C,OAAOC,yBAAc,CAACC,mBAAmB;YAC3C;YAEFsI;SACD;IACH;IAEA1E,kBAAkB,EAChBzF,QAAQ,EACRE,WAAW,EACXwF,OAAO,EACP1G,SAAS,EACT6F,UAAU,EACVuB,UAAU,EAQX,EAAE;QACD,MAAMmE,eAAe9B,MAAMC,IAAI,CAAChD,QAAQ/H,OAAO;QAE/C,6DAA6D;QAC7D,IAAI4M,aAAapM,MAAM,GAAG,KAAKqM,eAAM,CAACC,EAAE,CAACC,QAAQC,OAAO,EAAE,YAAY;YACpEzK,YAAY0K,MAAM,CAACrL,IAAI,CACrB,IAAIW,YAAYF,QAAQ,CAACO,OAAO,CAACsK,YAAY,CAC3C;YAIJ,OAAOxE,QAAQyE,OAAO;QACxB;QAEA,MAAMC,eAAe,CAAC,gCAAgC,EAAE7B,IAAAA,sBAAS,EAAC;YAChExD,SAASsF,KAAK9B,SAAS,CAACqB;YACxBU,qBAAqB7E;QACvB,GAAG,CAAC,CAAC;QAEL,MAAM8E,+BAA+B,IAAI,CAACtL,YAAY,GAClD/C,YAAYG,iBAAiB,GAC7BH,YAAYE,aAAa;QAC7B,KAAK,MAAM,CAACoO,GAAG3G,MAAM,IAAI+F,aAAc;YACrC,KAAK,MAAM/L,QAAQgG,MAAO;gBACxB,MAAM2B,KAAKiF,IAAAA,uBAAgB,EAACD,GAAG3M;gBAC/B,IAAI,OAAO0M,4BAA4B,CAAC/E,GAAG,KAAK,aAAa;oBAC3D+E,4BAA4B,CAAC/E,GAAG,GAAG;wBACjCkF,SAAS,CAAC;wBACV1J,OAAO,CAAC;oBACV;gBACF;gBACAuJ,4BAA4B,CAAC/E,GAAG,CAACkF,OAAO,CAACxG,WAAW,GAAG;gBACvDqG,4BAA4B,CAAC/E,GAAG,CAACxE,KAAK,CAACkD,WAAW,GAAGuB,aACjDxE,yBAAc,CAAC0J,aAAa,GAC5B1J,yBAAc,CAAC2J,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMC,iBAAiBjL,gBAAO,CAAC6J,WAAW,CAACC,gBAAgB,CAACU,cAAc;YACxEvM,MAAMqG;QACR;QAEA,OAAO,IAAI,CAACyF,QAAQ,CAClBpK,aACA,6BAA6B;QAC7BF,SAASgC,OAAO,EAChBwJ,gBACA;YACEhN,MAAMQ;YACN2C,OAAOyE,aACHxE,yBAAc,CAAC0J,aAAa,GAC5B1J,yBAAc,CAAC2J,qBAAqB;QAC1C;IAEJ;IAEAjB,SACEpK,WAAgB,EAChB8B,OAAe,EACfiC,UAA8B,EAC9BxE,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAI4G,QAAQ,CAACyE,SAASW;YAC3B,MAAMC,QAAQxL,YAAYvC,OAAO,CAACgO,GAAG,CAAClM,QAAQjB,IAAI;YAClDkN,MAAME,mBAAmB,CAACrM,IAAI,CAAC0E;YAC/B/D,YAAYD,KAAK,CAACqK,QAAQ,CAACuB,IAAI,CAACH,OAAOjM;YACvCS,YAAY4L,aAAa,CACvB;gBACE9J;gBACAiC;gBACA8H,aAAa;oBAAEC,aAAavM,QAAQkC,KAAK;gBAAC;YAC5C,GACA,CAACsK,KAAwBC;gBACvB,IAAID,KAAK;oBACP/L,YAAYD,KAAK,CAACkM,WAAW,CAACN,IAAI,CAAC5H,YAAYxE,SAASwM;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEA/L,YAAYD,KAAK,CAACmM,YAAY,CAACP,IAAI,CAAC5H,YAAYxE,SAASyM;gBACzD,OAAOpB,QAAQoB;YACjB;QAEJ;IACF;IAEA,MAAM/I,mBACJjD,WAAgC,EAChCgD,MAAqC,EACrC;QACA,MAAMnG,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDoF,IAAAA,uBAAe,EAAClC,aAAa,CAACiB,KAAKkB,QAAQgK,YAAYnL;YACrD,yEAAyE;YACzE,IACEmL,WAAW7N,IAAI,IACf2C,IAAIoB,OAAO,IACX,kCAAkCuG,IAAI,CAAC3H,IAAIoB,OAAO,GAClD;gBACA,MAAM6D,aAAa,4BAA4B0C,IAAI,CAAC3H,IAAIoB,OAAO;gBAE/D,MAAM+J,UAAU,IAAI,CAAC1M,YAAY,GAC7B/C,YAAYK,qBAAqB,GACjCL,YAAYI,iBAAiB;gBAEjC,IAAI,CAACqP,OAAO,CAACD,WAAW7N,IAAI,CAAC,EAAE;oBAC7B8N,OAAO,CAACD,WAAW7N,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACA8N,OAAO,CAACD,WAAW7N,IAAI,CAAC,CAAC4H,aAAa,WAAW,SAAS,GAAGlF;YAC/D;QACF;QAEA,IAAK,IAAIiF,MAAMtJ,YAAYE,aAAa,CAAE;YACxC,MAAMwP,SAAS1P,YAAYE,aAAa,CAACoJ,GAAG;YAC5C,IAAK,IAAI3H,QAAQ+N,OAAOlB,OAAO,CAAE;gBAC/B,MAAMnK,QACJrE,YAAYI,iBAAiB,CAACuB,KAAK,CACjC+N,OAAO5K,KAAK,CAACnD,KAAK,KAAKoD,yBAAc,CAAC0J,aAAa,GAC/C,WACA,SACL;gBACHiB,OAAOlB,OAAO,CAAC7M,KAAK,GAAG0C;YACzB;YACAnE,aAAa,CAACoJ,GAAG,GAAGoG;QACtB;QAEA,IAAK,IAAIpG,MAAMtJ,YAAYG,iBAAiB,CAAE;YAC5C,MAAMuP,SAAS1P,YAAYG,iBAAiB,CAACmJ,GAAG;YAChD,IAAK,IAAI3H,QAAQ+N,OAAOlB,OAAO,CAAE;gBAC/B,MAAMnK,QACJrE,YAAYK,qBAAqB,CAACsB,KAAK,CACrC+N,OAAO5K,KAAK,CAACnD,KAAK,KAAKoD,yBAAc,CAAC0J,aAAa,GAC/C,WACA,SACL;gBACHiB,OAAOlB,OAAO,CAAC7M,KAAK,GAAG0C;YACzB;YACAlE,iBAAiB,CAACmJ,GAAG,GAAGoG;QAC1B;QAEA,MAAMC,OAAOxB,KAAK9B,SAAS,CACzB;YACEuD,MAAM1P;YACN2P,MAAM1P;YAEN,oBAAoB;YACpB2P,eAAe,MAAMC,IAAAA,iDAA0B,EAAC,IAAI,CAAClN,GAAG;QAC1D,GACA,MACA,IAAI,CAACA,GAAG,GAAG,IAAImN;QAGjB3J,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEgN,qCAAyB,CAAC,GAAG,CAAC,CAAC,GAC1D,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,2BAA2B,EAAEhC,KAAK9B,SAAS,CAACsD,MAAM,CAAC;QAExDtJ,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEgN,qCAAyB,CAAC,KAAK,CAAC,CAAC,GAC5D,IAAIC,gBAAO,CAACC,SAAS,CAACR;IAC1B;AACF"}