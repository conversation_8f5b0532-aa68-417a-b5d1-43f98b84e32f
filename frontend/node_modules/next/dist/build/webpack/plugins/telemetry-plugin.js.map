{"version": 3, "sources": ["../../../../src/build/webpack/plugins/telemetry-plugin.ts"], "names": ["TelemetryPlugin", "FEATURE_MODULE_MAP", "Map", "FEATURE_MODULE_REGEXP_MAP", "BUILD_FEATURES", "ELIMINATED_PACKAGES", "Set", "findFeatureInModule", "module", "type", "normalizedIdentifier", "identifier", "replace", "feature", "path", "endsWith", "regexp", "test", "findUniqueOriginModulesInConnections", "connections", "originModule", "originModules", "connection", "has", "add", "constructor", "buildFeaturesMap", "usageTracker", "featureName", "set", "invocationCount", "get", "keys", "apply", "compiler", "hooks", "make", "tapAsync", "name", "compilation", "callback", "finishModules", "modules", "modulesFinish", "moduleGraph", "getIncomingConnections", "size", "options", "mode", "watchMode", "tap", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "eliminatedPackages", "usages", "values", "packagesUsedInServerSideProps", "Array", "from"], "mappings": ";;;;+BAiKaA;;;eAAAA;;;yBAhKgB;AAkE7B,sEAAsE;AACtE,MAAMC,qBAAmD,IAAIC,IAAI;IAC/D;QAAC;QAAc;KAAiB;IAChC;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAe;KAAkB;IAClC;QAAC;QAAgB;KAAmB;CACrC;AACD,MAAMC,4BAA0D,IAAID,IAAI;IACtE;QAAC;QAAqB;KAAwC;IAC9D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAmB;KAAsC;CAC3D;AAED,uDAAuD;AACvD,MAAME,iBAAiC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,sBAAsB,IAAIC;AAEhC;;CAEC,GACD,SAASC,oBAAoBC,MAAc;IACzC,IAAIA,OAAOC,IAAI,KAAK,mBAAmB;QACrC;IACF;IACA,MAAMC,uBAAuBF,OAAOG,UAAU,GAAGC,OAAO,CAAC,OAAO;IAChE,KAAK,MAAM,CAACC,SAASC,KAAK,IAAIb,mBAAoB;QAChD,IAAIS,qBAAqBK,QAAQ,CAACD,OAAO;YACvC,OAAOD;QACT;IACF;IACA,KAAK,MAAM,CAACA,SAASG,OAAO,IAAIb,0BAA2B;QACzD,IAAIa,OAAOC,IAAI,CAACP,uBAAuB;YACrC,OAAOG;QACT;IACF;AACF;AAEA;;;;CAIC,GACD,SAASK,qCACPC,WAAyB,EACzBC,YAAoB;IAEpB,MAAMC,gBAAgB,IAAIf;IAC1B,KAAK,MAAMgB,cAAcH,YAAa;QACpC,IACE,CAACE,cAAcE,GAAG,CAACD,WAAWF,YAAY,KAC1CE,WAAWF,YAAY,KAAKA,cAC5B;YACAC,cAAcG,GAAG,CAACF,WAAWF,YAAY;QAC3C;IACF;IACA,OAAOC;AACT;AAOO,MAAMrB;IAGX,qEAAqE;IACrEyB,YAAYC,gBAAuC,CAAE;aAH7CC,eAAe,IAAIzB;QAIzB,KAAK,MAAM0B,eAAexB,eAAgB;YACxC,IAAI,CAACuB,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiBJ,iBAAiBK,GAAG,CAACH,eAAe,IAAI;YAC3D;QACF;QAEA,KAAK,MAAMA,eAAe3B,mBAAmB+B,IAAI,GAAI;YACnD,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;QAEA,KAAK,MAAMF,eAAezB,0BAA0B6B,IAAI,GAAI;YAC1D,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;IACF;IAEAG,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAC1BrC,gBAAgBsC,IAAI,EACpB,OAAOC,aAAkCC;YACvCD,YAAYJ,KAAK,CAACM,aAAa,CAACJ,QAAQ,CACtCrC,gBAAgBsC,IAAI,EACpB,OAAOI,SAA2BC;gBAChC,KAAK,MAAMnC,UAAUkC,QAAS;oBAC5B,MAAM7B,UAAUN,oBAAoBC;oBACpC,IAAI,CAACK,SAAS;wBACZ;oBACF;oBACA,MAAMM,cAAc,AAClBoB,YACAK,WAAW,CAACC,sBAAsB,CAACrC;oBACrC,MAAMa,gBAAgBH,qCACpBC,aACAX;oBAEF,IAAI,CAACmB,YAAY,CAACI,GAAG,CAAClB,SAAUiB,eAAe,GAC7CT,cAAcyB,IAAI;gBACtB;gBACAH;YACF;YAEFH;QACF;QAEF,IAAIN,SAASa,OAAO,CAACC,IAAI,KAAK,gBAAgB,CAACd,SAASe,SAAS,EAAE;YACjEf,SAASC,KAAK,CAACI,WAAW,CAACW,GAAG,CAAClD,gBAAgBsC,IAAI,EAAE,CAACC;gBACpD,MAAMY,cAAcC,qBAAY,CAACC,mBAAmB,CAACd;gBACrDY,YAAYG,MAAM,CAACJ,GAAG,CAAClD,gBAAgBsC,IAAI,EAAE,CAACiB;oBAC5CA,cAAcC,kBAAkB,GAAGnD;gBACrC;YACF;QACF;IACF;IAEAoD,SAAyB;QACvB,OAAO;eAAI,IAAI,CAAC9B,YAAY,CAAC+B,MAAM;SAAG;IACxC;IAEAC,gCAA0C;QACxC,OAAOC,MAAMC,IAAI,CAACxD;IACpB;AACF"}