{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/Dialog.tsx"], "names": ["React", "useOnClickOutside", "Dialog", "children", "type", "onClose", "props", "dialog", "setDialog", "useState", "role", "setRole", "document", "hasFocus", "undefined", "onDialog", "useCallback", "node", "useEffect", "root", "getRootNode", "ShadowRoot", "shadowRoot", "handler", "e", "el", "activeElement", "key", "HTMLElement", "getAttribute", "preventDefault", "stopPropagation", "click", "handleFocus", "addEventListener", "window", "removeEventListener", "div", "ref", "data-nextjs-dialog", "tabIndex", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "data-nextjs-dialog-banner", "className"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,iBAAiB,QAAQ,mCAAkC;AAUpE,MAAMC,SAAgC,SAASA,OAAO,KAKrD;IALqD,IAAA,EACpDC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACP,GAAGC,OACJ,GALqD;IAMpD,MAAM,CAACC,QAAQC,UAAU,GAAGR,MAAMS,QAAQ,CAAwB;IAClE,MAAM,CAACC,MAAMC,QAAQ,GAAGX,MAAMS,QAAQ,CACpC,OAAOG,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAEN,MAAMC,WAAWf,MAAMgB,WAAW,CAAC,CAACC;QAClCT,UAAUS;IACZ,GAAG,EAAE;IACLhB,kBAAkBM,QAAQF;IAE1B,uEAAuE;IACvE,0BAA0B;IAC1BL,MAAMkB,SAAS,CAAC;QACd,IAAIX,UAAU,MAAM;YAClB;QACF;QAEA,MAAMY,OAAOZ,OAAOa,WAAW;QAC/B,8CAA8C;QAC9C,IAAI,CAAED,CAAAA,gBAAgBE,UAAS,GAAI;YACjC;QACF;QACA,MAAMC,aAAaH;QACnB,SAASI,QAAQC,CAAgB;YAC/B,MAAMC,KAAKH,WAAWI,aAAa;YACnC,IACEF,EAAEG,GAAG,KAAK,WACVF,cAAcG,eACdH,GAAGI,YAAY,CAAC,YAAY,QAC5B;gBACAL,EAAEM,cAAc;gBAChBN,EAAEO,eAAe;gBAEjBN,GAAGO,KAAK;YACV;QACF;QAEA,SAASC;YACP,2GAA2G;YAC3G,6EAA6E;YAC7EtB,QAAQC,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAQ,WAAWY,gBAAgB,CAAC,WAAWX;QACvCY,OAAOD,gBAAgB,CAAC,SAASD;QACjCE,OAAOD,gBAAgB,CAAC,QAAQD;QAChC,OAAO;YACLX,WAAWc,mBAAmB,CAAC,WAAWb;YAC1CY,OAAOC,mBAAmB,CAAC,SAASH;YACpCE,OAAOC,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG;QAAC1B;KAAO;IAEX,qBACE,oBAAC8B;QACCC,KAAKvB;QACLwB,sBAAAA;QACAC,UAAU,CAAC;QACX9B,MAAMA;QACN+B,mBAAiBnC,KAAK,CAAC,kBAAkB;QACzCoC,oBAAkBpC,KAAK,CAAC,mBAAmB;QAC3CqC,cAAW;qBAEX,oBAACN;QAAIO,6BAAAA;QAA0BC,WAAW,AAAC,YAASzC;QACnDD;AAGP;AAEA,SAASD,MAAM,GAAE"}