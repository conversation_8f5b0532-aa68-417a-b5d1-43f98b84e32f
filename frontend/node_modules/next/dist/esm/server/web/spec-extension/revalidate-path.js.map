{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate-path.ts"], "names": ["revalidateTag", "isDynamicRoute", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "revalidatePath", "originalPath", "type", "length", "console", "warn", "normalizedPath", "endsWith"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mBAAkB;AAChD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,0BAA0B,EAC1BC,8BAA8B,QACzB,yBAAwB;AAE/B,OAAO,SAASC,eAAeC,YAAoB,EAAEC,IAAwB;IAC3E,IAAID,aAAaE,MAAM,GAAGJ,gCAAgC;QACxDK,QAAQC,IAAI,CACV,CAAC,kCAAkC,EAAEJ,aAAa,+BAA+B,EAAEF,+BAA+B,uFAAuF,CAAC;QAE5M;IACF;IAEA,IAAIO,iBAAiB,CAAC,EAAER,2BAA2B,EAAEG,aAAa,CAAC;IAEnE,IAAIC,MAAM;QACRI,kBAAkB,CAAC,EAAEA,eAAeC,QAAQ,CAAC,OAAO,KAAK,IAAI,EAAEL,KAAK,CAAC;IACvE,OAAO,IAAIL,eAAeI,eAAe;QACvCG,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEJ,aAAa,kLAAkL,CAAC;IAErO;IACA,OAAOL,cAAcU;AACvB"}