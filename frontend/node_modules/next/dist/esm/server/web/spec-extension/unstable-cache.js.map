{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["maybePostpone", "staticGenerationAsyncStorage", "_staticGenerationAsyncStorage", "CACHE_ONE_YEAR", "addImplicitTags", "validateTags", "unstable_cache", "cb", "keyParts", "options", "fetch", "__nextGetStaticStore", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isUnstableCacheCallback", "isStaticGeneration", "experimental", "ppr", "tags", "tag", "includes", "push", "implicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": "AAAA,SAASA,aAAa,QAAQ,4CAA2C;AAKzE,SAASC,gCAAgCC,6BAA6B,QAAQ,sEAAqE;AACnJ,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,eAAe,EAAEC,YAAY,QAAQ,wBAAuB;AAIrE,OAAO,SAASC,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMR,+BACJ,CAAA,AAACS,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CR;IAE7C,IAAIO,QAAQG,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAEN,GAAGO,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;YAkDdC;QAjDX,MAAMA,QACJhB,gDAAAA,6BAA8BiB,QAAQ;QAExC,IAAID,SAAS,OAAOR,QAAQG,UAAU,KAAK,UAAU;YACnD,yEAAyE;YACzE,IAAIH,QAAQG,UAAU,KAAK,GAAG;gBAC5BZ,cAAciB,OAAO;gBACrB,+BAA+B;gBAC/BA,MAAML,UAAU,GAAG;YACnB,gIAAgI;YAClI,OAAO,IAAI,OAAOK,MAAML,UAAU,KAAK,UAAU;gBAC/C,IAAIK,MAAML,UAAU,GAAGH,QAAQG,UAAU,EAAE;oBACzCK,MAAML,UAAU,GAAGH,QAAQG,UAAU;gBACvC;YACA,oDAAoD;YACtD,OAAO;gBACLK,MAAML,UAAU,GAAGH,QAAQG,UAAU;YACvC;QACF;QAEA,MAAMO,mBAGJF,CAAAA,yBAAAA,MAAOE,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIN,MACR,CAAC,sDAAsD,EAAEN,GAAGO,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMQ,YAAY,CAAC,EAAEf,GAAGO,QAAQ,GAAG,CAAC,EAClCS,MAAMC,OAAO,CAAChB,aAAaA,SAASiB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACX,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOf,6BAA6B2B,GAAG,CACrC;YACE,GAAGX,KAAK;YACR,8DAA8D;YAC9D,8CAA8C;YAC9CY,YAAY;YACZC,aAAab,CAAAA,yBAAAA,MAAOa,WAAW,KAAI;YACnCC,yBAAyB;YACzBC,oBAAoBf,CAAAA,yBAAAA,MAAOe,kBAAkB,MAAK;YAClDC,cAAc;gBACZC,KAAKjB,CAAAA,0BAAAA,sBAAAA,MAAOgB,YAAY,qBAAnBhB,oBAAqBiB,GAAG,MAAK;YACpC;QACF,GACA;YACE,MAAMC,OAAO9B,aACXI,QAAQ0B,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAE5B,GAAGO,QAAQ,GAAG,CAAC;YAGnC,IAAIS,MAAMC,OAAO,CAACW,SAASlB,OAAO;gBAChC,IAAI,CAACA,MAAMkB,IAAI,EAAE;oBACflB,MAAMkB,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAMC,OAAOD,KAAM;oBACtB,IAAI,CAAClB,MAAMkB,IAAI,CAACE,QAAQ,CAACD,MAAM;wBAC7BnB,MAAMkB,IAAI,CAACG,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAetB,QAAQb,gBAAgBa,SAAS,EAAE;YAExD,MAAMuB,WAAW,OAAMrB,oCAAAA,iBAAkBsB,aAAa,CAACnB;YACvD,MAAMoB,aACJF,YACA,sDAAsD;YACtD,4CAA4C;YAC5CvB,CAAAA,yBAAAA,MAAOY,UAAU,MAAK,oBACtB,CACEZ,CAAAA,CAAAA,yBAAAA,MAAO0B,oBAAoB,KAAIxB,iBAAiBwB,oBAAoB,AAAD,KAEpE,OAAMxB,oCAAAA,iBAAkByB,GAAG,CAACJ,UAAU;gBACrCX,YAAY;gBACZjB,YAAYH,QAAQG,UAAU;gBAC9BuB;gBACAU,UAAUN;YACZ;YAEF,MAAMO,iBAAiB;gBACrB,MAAMC,SAAS,MAAMxC,MAAMS;gBAE3B,IAAIwB,YAAYrB,kBAAkB;oBAChC,MAAMA,iBAAiB6B,GAAG,CACxBR,UACA;wBACES,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAM1B,KAAKC,SAAS,CAACoB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACA1C,YACE,OAAOH,QAAQG,UAAU,KAAK,WAC1BT,iBACAM,QAAQG,UAAU;oBAC1B,GACA;wBACEA,YAAYH,QAAQG,UAAU;wBAC9BiB,YAAY;wBACZM;oBACF;gBAEJ;gBACA,OAAOY;YACT;YAEA,IAAI,CAACL,cAAc,CAACA,WAAWa,KAAK,EAAE;gBACpC,OAAOT;YACT;YAEA,IAAIJ,WAAWa,KAAK,CAACN,IAAI,KAAK,SAAS;gBACrCO,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEnC,UAAU,CAAC;gBAE1D,OAAOwB;YACT;YACA,IAAIY;YACJ,MAAMC,UAAUjB,WAAWiB,OAAO;YAElC,IAAIjB,YAAY;gBACd,MAAMkB,UAAUlB,WAAWa,KAAK,CAACL,IAAI;gBACrCQ,cAAchC,KAAKmC,KAAK,CAACD,QAAQR,IAAI;YACvC;YAEA,IAAIO,SAAS;gBACX,IAAI,CAAC1C,OAAO;oBACV,OAAO6B;gBACT,OAAO;oBACL,IAAI,CAAC7B,MAAM6C,kBAAkB,EAAE;wBAC7B7C,MAAM6C,kBAAkB,GAAG,EAAE;oBAC/B;oBACA7C,MAAM6C,kBAAkB,CAACxB,IAAI,CAC3BQ,iBAAiBiB,KAAK,CAAC,CAACC,MACtBR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAEnC,UAAU,CAAC,EAAE0C;gBAGjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAO3C;AACT"}