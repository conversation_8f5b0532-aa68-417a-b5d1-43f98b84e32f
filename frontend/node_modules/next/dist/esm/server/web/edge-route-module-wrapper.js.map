{"version": 3, "sources": ["../../../src/server/web/edge-route-module-wrapper.ts"], "names": ["adapter", "IncrementalCache", "RouteMatcher", "removeTrailingSlash", "removePathPrefix", "internal_getCurrentFunctionWaitUntil", "EdgeRouteModuleWrapper", "routeModule", "matcher", "definition", "wrap", "options", "wrapper", "opts", "handler", "bind", "request", "evt", "pathname", "URL", "url", "basePath", "nextUrl", "match", "Error", "prerenderManifest", "self", "__PRERENDER_MANIFEST", "JSON", "parse", "undefined", "context", "params", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "supportsDynamicHTML", "ppr", "res", "handle", "waitUntilPromises", "waitUntil", "push", "Promise", "all"], "mappings": "AAOA,OAAO,YAAW;AAElB,SAASA,OAAO,QAA6B,YAAW;AACxD,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,YAAY,QAAQ,yCAAwC;AACrE,SAASC,mBAAmB,QAAQ,sDAAqD;AACzF,SAASC,gBAAgB,QAAQ,mDAAkD;AAEnF,SAASC,oCAAoC,QAAQ,6BAA4B;AAIjF;;;;CAIC,GACD,OAAO,MAAMC;IAGX;;;;;GAKC,GACD,YAAqCC,YAAkC;2BAAlCA;QACnC,wEAAwE;QACxE,IAAI,CAACC,OAAO,GAAG,IAAIN,aAAaK,YAAYE,UAAU;IACxD;IAEA;;;;;;;;GAQC,GACD,OAAcC,KACZH,WAAgC,EAChCI,UAAuB,CAAC,CAAC,EACzB;QACA,6BAA6B;QAC7B,MAAMC,UAAU,IAAIN,uBAAuBC;QAE3C,gCAAgC;QAChC,OAAO,CAACM;YACN,OAAOb,QAAQ;gBACb,GAAGa,IAAI;gBACP,GAAGF,OAAO;gBACVV;gBACA,kEAAkE;gBAClEa,SAASF,QAAQE,OAAO,CAACC,IAAI,CAACH;YAChC;QACF;IACF;IAEA,MAAcE,QACZE,OAAoB,EACpBC,GAAmB,EACA;QACnB,uEAAuE;QACvE,wBAAwB;QACxB,IAAIC,WAAWf,oBAAoB,IAAIgB,IAAIH,QAAQI,GAAG,EAAEF,QAAQ;QAEhE,iEAAiE;QACjE,MAAM,EAAEG,QAAQ,EAAE,GAAGL,QAAQM,OAAO;QACpC,IAAID,UAAU;YACZ,+DAA+D;YAC/DH,WAAWd,iBAAiBc,UAAUG;QACxC;QAEA,kCAAkC;QAClC,MAAME,QAAQ,IAAI,CAACf,OAAO,CAACe,KAAK,CAACL;QACjC,IAAI,CAACK,OAAO;YACV,MAAM,IAAIC,MACR,CAAC,iDAAiD,EAAEN,SAAS,uBAAuB,EAAE,IAAI,CAACV,OAAO,CAACC,UAAU,CAACS,QAAQ,CAAC,CAAC,CAAC;QAE7H;QAEA,MAAMO,oBACJ,OAAOC,KAAKC,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACH,KAAKC,oBAAoB,IACpCG;QAEN,wEAAwE;QACxE,kBAAkB;QAClB,MAAMC,UAAuC;YAC3CC,QAAQT,MAAMS,MAAM;YACpBP,mBAAmB;gBACjBQ,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAASX,CAAAA,qCAAAA,kBAAmBW,OAAO,KAAI;oBACrCC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB;YACAC,YAAY;gBACVC,qBAAqB;gBACrB,mCAAmC;gBACnCC,KAAK;YACP;QACF;QAEA,qCAAqC;QACrC,MAAMC,MAAM,MAAM,IAAI,CAACrC,WAAW,CAACsC,MAAM,CAAC7B,SAASe;QAEnD,MAAMe,oBAAoB;YAACzC;SAAuC;QAClE,IAAI0B,QAAQU,UAAU,CAACM,SAAS,EAAE;YAChCD,kBAAkBE,IAAI,CAACjB,QAAQU,UAAU,CAACM,SAAS;QACrD;QACA9B,IAAI8B,SAAS,CAACE,QAAQC,GAAG,CAACJ;QAE1B,OAAOF;IACT;AACF"}