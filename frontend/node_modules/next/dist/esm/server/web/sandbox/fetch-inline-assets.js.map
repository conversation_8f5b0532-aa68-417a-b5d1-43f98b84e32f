{"version": 3, "sources": ["../../../../src/server/web/sandbox/fetch-inline-assets.ts"], "names": ["createReadStream", "promises", "fs", "requestToBodyStream", "resolve", "fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "hash", "replace", "asset", "assets", "find", "x", "name", "filePath", "distDir", "fileIsReadable", "access", "then", "readStream", "context", "Response", "Uint8Array"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACrD,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,OAAO,QAAQ,OAAM;AAE9B;;;;CAIC,GACD,OAAO,eAAeC,iBAAiBC,OAKtC;QAOeA;IANd,MAAMC,cAAcC,OAAOF,QAAQG,KAAK;IACxC,IAAI,CAACF,YAAYG,UAAU,CAAC,UAAU;QACpC;IACF;IAEA,MAAMC,OAAOJ,YAAYK,OAAO,CAAC,SAAS;IAC1C,MAAMC,SAAQP,kBAAAA,QAAQQ,MAAM,qBAAdR,gBAAgBS,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKN;IACrD,IAAI,CAACE,OAAO;QACV;IACF;IAEA,MAAMK,WAAWd,QAAQE,QAAQa,OAAO,EAAEN,MAAMK,QAAQ;IACxD,MAAME,iBAAiB,MAAMlB,GAAGmB,MAAM,CAACH,UAAUI,IAAI,CACnD,IAAM,MACN,IAAM;IAGR,IAAIF,gBAAgB;QAClB,MAAMG,aAAavB,iBAAiBkB;QACpC,OAAO,IAAIZ,QAAQkB,OAAO,CAACC,QAAQ,CACjCtB,oBAAoBG,QAAQkB,OAAO,EAAEE,YAAYH;IAErD;AACF"}