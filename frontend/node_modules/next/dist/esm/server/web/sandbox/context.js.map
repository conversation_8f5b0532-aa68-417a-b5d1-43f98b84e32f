{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["AsyncLocalStorage", "COMPILER_NAMES", "EDGE_UNSUPPORTED_NODE_APIS", "EdgeRuntime", "readFileSync", "promises", "fs", "validateURL", "pick", "fetchInlineAsset", "runInContext", "BufferImplementation", "EventsImplementation", "AssertImplementation", "UtilImplementation", "AsyncHooksImplementation", "intervalsManager", "timeouts<PERSON><PERSON><PERSON>", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "moduleContexts", "Map", "pendingModuleCaches", "clearModuleContext", "path", "removeAll", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "entries", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "assets", "distDir", "headers", "Headers", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "err", "message", "stack", "__Request", "Request", "constructor", "next", "__redirect", "Response", "redirect", "bind", "args", "assign", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "getModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "filename"], "mappings": "AAMA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SACEC,cAAc,EACdC,0BAA0B,QACrB,gCAA+B;AACtC,SAASC,WAAW,QAAQ,kCAAiC;AAC7D,SAASC,YAAY,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACjD,SAASC,WAAW,QAAQ,WAAU;AACtC,SAASC,IAAI,QAAQ,oBAAmB;AACxC,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,wBAAwB,YAAW;AAC1C,OAAOC,8BAA8B,mBAAkB;AACvD,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,sBAAqB;AAQvE,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBAAsBI,WAAWJ,mBAAmB;AACtD,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACM,OAAcC,IAAcD;AACrD;AAEA;;;;CAIC,GACD,MAAME,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAEhC;;;;;;;CAOC,GACD,OAAO,eAAeE,mBAAmBC,IAAY;IACnDf,iBAAiBgB,SAAS;IAC1Bf,gBAAgBe,SAAS;IAEzB,MAAMC,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACP,OAAO;YAC1BK,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIR,eAAgB;QACzCM,cAAcC,KAAKC,OAAOR;IAC5B;IACA,KAAK,MAAM,CAACO,KAAKC,MAAM,IAAIN,oBAAqB;QAC9CI,cAAcC,KAAK,MAAMC,OAAON;IAClC;AACF;AAEA,eAAeW,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,SAAS,MAAMC,YAAYC,OAAO,CACtC,MAAM3C,GAAG4C,QAAQ,CAACJ,QAAQK,QAAQ;QAEpCT,OAAO,CAACI,QAAQM,IAAI,CAAC,GAAGL;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASW;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACpC,QAAQC,GAAG,EAAEwB,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKd,QAAQC,GAAG,CAACa,IAAI;SAAC;IAC3E,MAAMb,MAAMkC,OAAOE,WAAW,CAACH;IAC/BjC,IAAIqC,YAAY,GAAG;IACnB,OAAOrC;AACT;AAEA,SAASsC,yBAAyBP,IAAY;IAC5C,MAAM3B,QACJ,IAAImC,MAAM,CAAC,uBAAuB,EAAER,KAAK;8DACiB,CAAC;IAC7DjC,oBAAoBM,OAAOxB,eAAe4D,UAAU;IACpD,MAAMpC;AACR;AAEA,SAASqC;IACP,MAAMC,kBAAkB;QAAE1C,KAAKgC;IAAgC;IAC/D,MAAMW,iBAAsC,CAAC;IAC7C,KAAK,MAAM9B,OAAOqB,OAAOC,IAAI,CAACpC,SAAU;QACtC,IAAIc,QAAQ,OAAO;QACnBqB,OAAOU,cAAc,CAACF,iBAAiB7B,KAAK;YAC1CgC;gBACE,IAAIF,cAAc,CAAC9B,IAAI,KAAKiC,WAAW;oBACrC,OAAOH,cAAc,CAAC9B,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACd,OAAe,CAACc,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAMyB,yBAAyB,CAAC,QAAQ,EAAEzB,IAAI,CAAC;gBACxD;gBACA,OAAOiC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAAC9B,IAAI,GAAGmC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQnC,OAA+B,EAAEgB,IAAY;IAC5DG,OAAOU,cAAc,CAAC7B,SAASgB,MAAM;QACnCc;YACE,OAAO;gBACLP,yBAAyBP;YAC3B;QACF;QACAkB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAAClD;QACN,IAAIA,iBAAiBiD,kBAAkB;YACrCvD,oBAAoBM,OAAOxB,eAAe4D,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/CvD,oBAAoB0D,SAASC,MAAM,EAAE7E,eAAe4D,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAexE,KAAKG,sBAAsB;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeH,KAAKI,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBJ,KAAKO,0BAA0B;YACjD;YACA;SACD;QACD,eAAeP,KAAKK,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaL,KAAKM,oBAAoB;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAIc,IAAI2B,OAAO0B,OAAO,CAACD;AAChC,CAAA;AAEA;;;CAGC,GACD,eAAeE,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM5C,OAAO,MAAMD,SAAS2C,QAAQI,iBAAiB,CAAC9C,IAAI,IAAI,EAAE;IAChE,MAAMgC,UAAU,IAAItE,YAAY;QAC9BqF,gBACEpE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEmE,SAAS;YAAMhD,MAAM;QAAK,IAC5B0B;QACNuB,QAAQ,CAACtD;YACPA,QAAQhB,OAAO,GAAG0C;YAElBP,OAAOU,cAAc,CAAC7B,SAAS,WAAW;gBACxCkC,YAAY;gBACZD,OAAO,CAACsB;oBACN,MAAMtB,QAAQU,gBAAgBb,GAAG,CAACyB;oBAClC,IAAI,CAACtB,OAAO;wBACV,MAAMuB,UAAU,8BAA8BD;oBAChD;oBACA,OAAOtB;gBACT;YACF;YAEAjC,QAAQyD,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAM5D,MAAM4D,GAAGC,QAAQ;gBACvB,IAAI,CAACX,YAAY9C,GAAG,CAACJ,MAAM;oBACzB,MAAM8D,UAAU9E,eACd,IAAI0C,MACF,CAAC;yEAC0D,CAAC,GAE9D3D,eAAe4D,UAAU;oBAE3BmC,QAAQ5C,IAAI,GAAG;oBACfQ,MAAMqC,iBAAiB,CAACD,SAASH;oBACjCT,YAAYc,GAAG,CAAChE;oBAChBiD,QAAQgB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEA1D,QAAQgE,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAM5D,MAAM4D,GAAGC,QAAQ;gBACvB,IAAI,CAACT,mBAAmBhD,GAAG,CAACJ,MAAM;oBAChC,MAAM8D,UAAU9E,eACd,IAAI0C,MAAM,CAAC;yEACgD,CAAC,GAC5D3D,eAAe4D,UAAU;oBAE3BmC,QAAQ5C,IAAI,GAAG;oBACfQ,MAAMqC,iBAAiB,CAACD,SAASI;oBACjCd,mBAAmBY,GAAG,CAAChE;oBACvBiD,QAAQgB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEF1D,QAAQiE,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAMtE,MAAM4D,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACjB,mBAAmBhD,GAAG,CAACJ,MAAM;oBAC1D,MAAM8D,UAAU9E,eACd,IAAI0C,MAAM,CAAC;yEACgD,CAAC,GAC5D3D,eAAe4D,UAAU;oBAE3BmC,QAAQ5C,IAAI,GAAG;oBACfQ,MAAMqC,iBAAiB,CAACD,SAASK;oBACjCf,mBAAmBY,GAAG,CAAChE;oBACvBiD,QAAQgB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUrE,QAAQsE,KAAK;YAC7BtE,QAAQsE,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBAcnCA;gBAbF,MAAMC,eAAe,IAAIjD,MAAM;gBAC/B,MAAMkD,gBAAgB,MAAMrG,iBAAiB;oBAC3CkG;oBACAI,QAAQ5B,QAAQI,iBAAiB,CAACwB,MAAM;oBACxCC,SAAS7B,QAAQ6B,OAAO;oBACxB5E;gBACF;gBACA,IAAI0E,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKK,OAAO,GAAG,IAAIC,QAAQN,KAAKK,OAAO,IAAI,CAAC;gBAC5C,MAAME,QACJP,EAAAA,oBAAAA,KAAKK,OAAO,CAAC/C,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1C0C,kBAA6CQ,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAM/C,QAAQ8C,MAAME,MAAM,CAAClC,QAAQmC,UAAU,EAAEC,IAAI,CAAC;gBACpDX,KAAKK,OAAO,CAAC7C,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAACuC,KAAKK,OAAO,CAAC3E,GAAG,CAAC,eAAe;oBACnCsE,KAAKK,OAAO,CAAC7C,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAMoD,WACJ,OAAOb,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMc,GAAG,EAAE;oBACjB,GAAGjH,KAAKmG,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPK,SAAS;wBACP,GAAG1D,OAAOE,WAAW,CAACkD,MAAMM,OAAO,CAAC;wBACpC,GAAG1D,OAAOE,WAAW,CAACmD,KAAKK,OAAO,CAAC;oBACrC;gBACF,KACAR,QAAQiB,OAAOf,QAAQC;gBAE7B,OAAO,MAAMY,SAASG,KAAK,CAAC,CAACC;oBAC3Bf,aAAagB,OAAO,GAAGD,IAAIC,OAAO;oBAClCD,IAAIE,KAAK,GAAGjB,aAAaiB,KAAK;oBAC9B,MAAMF;gBACR;YACF;YAEA,MAAMG,YAAY3F,QAAQ4F,OAAO;YACjC5F,QAAQ4F,OAAO,GAAG,cAAcD;gBAE9BE,YAAYtB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMa,MACJ,OAAOd,UAAU,YAAY,SAASA,QAClCA,MAAMc,GAAG,GACTC,OAAOf;oBACbpG,YAAYkH;oBACZ,KAAK,CAACA,KAAKb;oBACX,IAAI,CAACsB,IAAI,GAAGtB,wBAAAA,KAAMsB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAa/F,QAAQgG,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAClG,QAAQgG,QAAQ;YAClEhG,QAAQgG,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BhI,YAAYgI,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMnF,QAAQlD,2BAA4B;gBAC7CqE,QAAQnC,SAASgB;YACnB;YAEAG,OAAOiF,MAAM,CAACpG,SAASK;YAEvBL,QAAQpC,iBAAiB,GAAGA;YAE5B,+DAA+D;YAC/DoC,QAAQqG,WAAW,GAAG,CAAC,GAAGF,OACxBvH,iBAAiBkF,GAAG,CAACqC;YAEvB,+DAA+D;YAC/DnG,QAAQsG,aAAa,GAAG,CAACC,WACvB3H,iBAAiB4H,MAAM,CAACD;YAE1B,+DAA+D;YAC/DvG,QAAQyG,UAAU,GAAG,CAAC,GAAGN,OACvBtH,gBAAgBiF,GAAG,CAACqC;YAEtB,+DAA+D;YAC/DnG,QAAQ0G,YAAY,GAAG,CAACC,UACtB9H,gBAAgB2H,MAAM,CAACG;YAEzB,OAAO3G;QACT;IACF;IAEA,MAAM4G,yBAAyBxE,0BAA0BC;IACzDA,QAAQrC,OAAO,CAAC6G,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6BtE,8BAA8BH;IACjEA,QAAQrC,OAAO,CAAC6G,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACLzE;QACApC,OAAO,IAAIT;QACXwD,aAAa,IAAIC;IACnB;AACF;AAUA,SAAS8D,uBAAuBhE,OAA6B;IAC3D,IAAIiE,wBAAwBvH,oBAAoBqC,GAAG,CAACiB,QAAQmC,UAAU;IACtE,IAAI,CAAC8B,uBAAuB;QAC1BA,wBAAwBlE,oBAAoBC;QAC5CtD,oBAAoBuC,GAAG,CAACe,QAAQmC,UAAU,EAAE8B;IAC9C;IACA,OAAOA;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,iBAAiBlE,OAA6B;IAMlE,IAAImE;IAIJ,IAAInE,QAAQoE,QAAQ,EAAE;QACpBD,oBACE3H,eAAeuC,GAAG,CAACiB,QAAQmC,UAAU,KACpC,MAAM6B,uBAAuBhE;IAClC;IAEA,IAAI,CAACmE,mBAAmB;QACtBA,oBAAoB,MAAMpE,oBAAoBC;QAC9CxD,eAAeyC,GAAG,CAACe,QAAQmC,UAAU,EAAEgC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAcnH,KAAK,CAACC,GAAG,CAACoH,WAAW;YACtC,MAAMC,UAAUvJ,aAAasJ,UAAU;YACvC,IAAI;gBACFhJ,aAAaiJ,SAASH,cAAc/E,OAAO,CAACrC,OAAO,EAAE;oBACnDwH,UAAUF;gBACZ;gBACAF,cAAcnH,KAAK,CAAC+B,GAAG,CAACsF,UAAUC;YACpC,EAAE,OAAOlI,OAAO;gBACd,IAAI0D,QAAQoE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAenH,KAAK,CAACE,MAAM,CAACmH;gBAC9B;gBACA,MAAMjI;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAG+H,aAAa;QAAEC;IAAkB;AAC/C"}