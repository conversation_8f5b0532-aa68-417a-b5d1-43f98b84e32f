{"version": 3, "sources": ["../../../../../../../src/server/future/route-modules/app-page/vendored/rsc/entrypoints.ts"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackServerEdge", "ReactServerDOMWebpackServerEdge", "ReactServerDOMTurbopackServerNode", "ReactServerDOMWebpackServerNode", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,kCAAiC;AAC3D,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AAEpD,SAASC,0BACPC,IAA6B,EAC7BC,GAI0C;IAE1C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC;YAE3N;QACF;IAEJ;AACF;AAEA,IAAIS,mCAAmCC;AACvC,IAAIC,mCAAmCC;AAEvC,IAAIhB,QAAQC,GAAG,CAACgB,SAAS,EAAE;IACzB,6DAA6D;IAC7DJ,oCAAoCK,QAAQ;IAC5C,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,kCAAkCjB,0BAChC,aACA;IAEJ;IACA,6DAA6D;IAC7DkB,oCAAoCG,QAAQ;IAC5C,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Cc,kCAAkCnB,0BAChC,aACA;IAEJ;AACF,OAAO;IACL,6DAA6D;IAC7DiB,kCAAkCI,QAAQ;IAC1C,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,oCAAoChB,0BAClC,WACA;IAEJ;IACA,6DAA6D;IAC7DmB,kCAAkCE,QAAQ;IAC1C,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Ca,oCAAoClB,0BAClC,WACA;IAEJ;AACF;AAEA,SACEJ,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfF,QAAQ,EACRoB,+BAA+B,EAC/BD,iCAAiC,EACjCG,+BAA+B,EAC/BD,iCAAiC,KAClC"}