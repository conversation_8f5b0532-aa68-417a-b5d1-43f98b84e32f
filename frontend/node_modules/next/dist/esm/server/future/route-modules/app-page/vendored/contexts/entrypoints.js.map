{"version": 3, "sources": ["../../../../../../../src/server/future/route-modules/app-page/vendored/contexts/entrypoints.ts"], "names": ["HeadManagerContext", "ServerInsertedHtml", "AppRouterContext", "HooksClientContext", "RouterContext", "HtmlContext", "AmpContext", "LoadableContext", "ImageConfigContext", "Loadable"], "mappings": "AAAA,OAAO,KAAKA,kBAAkB,MAAM,mEAAkE;AACtG,OAAO,KAAKC,kBAAkB,MAAM,mEAAkE;AACtG,OAAO,KAAKC,gBAAgB,MAAM,iEAAgE;AAClG,OAAO,KAAKC,kBAAkB,MAAM,mEAAkE;AACtG,OAAO,KAAKC,aAAa,MAAM,6DAA4D;AAC3F,OAAO,KAAKC,WAAW,MAAM,2DAA0D;AACvF,OAAO,KAAKC,UAAU,MAAM,0DAAyD;AACrF,OAAO,KAAKC,eAAe,MAAM,+DAA8D;AAC/F,OAAO,KAAKC,kBAAkB,MAAM,mEAAkE;AACtG,OAAO,KAAKC,QAAQ,MAAM,uDAAsD"}