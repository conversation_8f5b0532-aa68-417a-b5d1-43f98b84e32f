{"version": 3, "sources": ["../../../../src/server/typescript/rules/config.ts"], "names": ["getSource", "isPositionInsideNode", "getTs", "removeStringQuotes", "NEXT_TS_ERRORS", "ALLOWED_EXPORTS", "LEGACY_CONFIG_EXPORT", "API_DOCS", "dynamic", "description", "options", "link", "fetchCache", "preferredRegion", "<PERSON><PERSON><PERSON><PERSON>", "value", "parsed", "JSON", "parse", "Array", "isArray", "some", "v", "err", "getHint", "join", "revalidate", "type", "false", "Number", "replace", "dynamicParams", "true", "runtime", "metadata", "visitEntryConfig", "fileName", "position", "callback", "source", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "text", "name", "getText", "createAutoCompletionOptionName", "sort", "sortText", "ScriptElementKind", "constElement", "kindModifiers", "ScriptElementKindModifier", "exportedModifier", "labelDetails", "data", "exportName", "moduleSpecifier", "createAutoCompletionOptionValue", "apiName", "isString", "startsWith", "insertText", "string", "unknown", "none", "getAPIDescription", "api", "Object", "entries", "map", "key", "config", "addCompletionsAtPosition", "prior", "entryConfig", "push", "keys", "index", "getQuickInfoAtPosition", "overridden", "initializer", "docsLink", "isStringLiteral", "enumElement", "textSpan", "start", "getStart", "length", "getWidth", "displayParts", "documentation", "getCompletionEntryDetails", "entryName", "content", "getSemanticDiagnosticsForExportVariableStatement", "diagnostics", "isIdentifier", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_ENTRY_EXPORT", "messageText", "displayedValue", "errorMessage", "isInvalid", "isNoSubstitutionTemplateLiteral", "val", "<PERSON><PERSON><PERSON><PERSON>", "filter", "test", "isNumericLiteral", "isPrefixUnaryExpression", "isMinusToken", "operator", "operand", "TrueKeyword", "FalseKeyword", "isArrayLiteralExpression", "stringify", "elements", "e", "isBigIntLiteral", "isObjectLiteralExpression", "isRegularExpressionLiteral", "INVALID_OPTION_VALUE", "prop", "properties", "isPropertyAssignment", "INVALID_CONFIG_OPTION"], "mappings": "AAAA,4EAA4E;AAE5E,SACEA,SAAS,EACTC,oBAAoB,EACpBC,KAAK,EACLC,kBAAkB,QACb,WAAU;AACjB,SACEC,cAAc,EACdC,eAAe,EACfC,oBAAoB,QACf,cAAa;AAGpB,MAAMC,WAUF;IACFC,SAAS;QACPC,aACE;QACFC,SAAS;YACP,UACE;YACF,mBACE;YACF,WACE;YACF,kBACE;QACJ;QACAC,MAAM;IACR;IACAC,YAAY;QACVH,aACE;QACFC,SAAS;YACP,oBACE;YACF,mBACE;YACF,sBACE;YACF,UACE;YACF,mBACE;YACF,gBACE;YACF,iBACE;QACJ;QACAC,MAAM;IACR;IACAE,iBAAiB;QACfJ,aACE;QACFC,SAAS;YACP,UACE;YACF,YAAY;YACZ,UAAU;QACZ;QACAC,MAAM;QACNG,SAAS,CAACC;YACR,IAAI;gBACF,MAAMC,SAASC,KAAKC,KAAK,CAACH;gBAC1B,OACE,OAAOC,WAAW,YACjBG,MAAMC,OAAO,CAACJ,WAAW,CAACA,OAAOK,IAAI,CAAC,CAACC,IAAM,OAAOA,MAAM;YAE/D,EAAE,OAAOC,KAAK;gBACZ,OAAO;YACT;QACF;QACAC,SAAS,CAACT;YACR,IAAIA,UAAU,QAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC/D,IAAIA,UAAU,UAAU,OAAO,CAAC,0BAA0B,CAAC;YAC3D,IAAIA,UAAU,QAAQ,OAAO,CAAC,oCAAoC,CAAC;YACnE,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAO,CAAC,mBAAmB,EAAEA,MAAMU,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,OAAOV,UAAU,UAAU,OAAO,CAAC,kBAAkB,EAAEA,MAAM,CAAC,CAAC;QACrE;IACF;IACAW,YAAY;QACVjB,aACE;QACFkB,MAAM;QACNjB,SAAS;YACPkB,OACE;YACF,GAAG;YACH,IAAI;QACN;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,WAAWc,OAAOd,MAAMe,OAAO,CAAC,MAAM,QAAQ;QACjE;QACAN,SAAS,CAACT;YACR,OAAO,CAAC,uCAAuC,EAAEA,MAAM,WAAW,CAAC;QACrE;IACF;IACAgB,eAAe;QACbtB,aACE;QACFC,SAAS;YACPsB,MAAM;YACNJ,OACE;QACJ;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;IACAkB,SAAS;QACPxB,aACE;QACFC,SAAS;YACP,YAAY;YACZ,UAAU;YACV,uBAAuB;QACzB;QACAC,MAAM;IACR;IACAuB,UAAU;QACRzB,aAAa;QACbE,MAAM;IACR;AACF;AAEA,SAASwB,iBACPC,QAAgB,EAChBC,QAAgB,EAChBC,QAA4E;IAE5E,MAAMC,SAASvC,UAAUoC;IACzB,IAAIG,QAAQ;QACV,MAAMC,KAAKtC;QACXsC,GAAGC,YAAY,CAACF,QAAQ,SAASG,MAAMC,IAAI;YACzC,uBAAuB;YACvB,IAAI1C,qBAAqBoC,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBtB,IAAI,CAAC,CAACyB,IAAMA,EAAEC,IAAI,KAAKP,GAAGQ,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIT,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;4BAC3D,IAAIpD,qBAAqBoC,UAAUe,cAAc;gCAC/C,2BAA2B;gCAC3B,MAAME,OAAOF,YAAYG,IAAI,CAACC,OAAO;gCACrClB,SAASgB,MAAMF;4BACjB;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASK,+BAA+BC,IAAY,EAAEH,IAAY;IAChE,MAAMf,KAAKtC;IACX,OAAO;QACLqD;QACAI,UAAU,MAAMD;QAChBX,MAAMP,GAAGoB,iBAAiB,CAACC,YAAY;QACvCC,eAAetB,GAAGuB,yBAAyB,CAACC,gBAAgB;QAC5DC,cAAc;YACZxD,aAAa,CAAC,QAAQ,EAAE8C,KAAK,OAAO,CAAC;QACvC;QACAW,MAAM;YACJC,YAAYZ;YACZa,iBAAiB;QACnB;IACF;AACF;AAEA,SAASC,gCACPX,IAAY,EACZH,IAAY,EACZe,OAAe;IAEf,MAAM9B,KAAKtC;IACX,MAAMqE,WAAWhB,KAAKiB,UAAU,CAAC;IACjC,OAAO;QACLjB;QACAkB,YAAYtE,mBAAmBoD;QAC/BI,UAAU,KAAKD;QACfX,MAAMwB,WAAW/B,GAAGoB,iBAAiB,CAACc,MAAM,GAAGlC,GAAGoB,iBAAiB,CAACe,OAAO;QAC3Eb,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;QAChDX,cAAc;YACZxD,aAAa,CAAC,QAAQ,EAAE6D,QAAQ,OAAO,CAAC;QAC1C;QACAJ,MAAM;YACJC,YAAYG;YACZF,iBAAiB;QACnB;IACF;AACF;AAEA,SAASS,kBAAkBC,GAAW;IACpC,OACEvE,QAAQ,CAACuE,IAAI,CAACrE,WAAW,GACzB,SACAsE,OAAOC,OAAO,CAACzE,QAAQ,CAACuE,IAAI,CAACpE,OAAO,IAAI,CAAC,GACtCuE,GAAG,CAAC,CAAC,CAACC,KAAKnE,MAAM,GAAK,CAAC,IAAI,EAAEmE,IAAI,IAAI,EAAEnE,MAAM,CAAC,EAC9CU,IAAI,CAAC;AAEZ;AACA,MAAM0D,SAAS;IACb,8CAA8C;IAC9CC,0BACEhD,QAAgB,EAChBC,QAAgB,EAChBgD,KAAqD;QAErDlD,iBAAiBC,UAAUC,UAAU,CAACiD,aAAalC;YACjD,IAAI,CAAC7C,QAAQ,CAAC+E,YAAY,EAAE;gBAC1B,IAAIrF,qBAAqBoC,UAAUe,YAAYG,IAAI,GAAG;oBACpD8B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACjF,UAAU0E,GAAG,CAAC,CAAC1B,MAAMkC;wBAClC,OAAOhC,+BAA+BgC,OAAOlC;oBAC/C;gBAEJ;gBACA;YACF;YAEA8B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACjF,QAAQ,CAAC+E,YAAY,CAAC5E,OAAO,IAAI,CAAC,GAAGuE,GAAG,CACrD,CAAC1B,MAAMkC;gBACL,OAAOpB,gCAAgCoB,OAAOlC,MAAM+B;YACtD;QAGN;IACF;IAEA,mDAAmD;IACnDI,wBAAuBtD,QAAgB,EAAEC,QAAgB;QACvD,MAAMG,KAAKtC;QAEX,IAAIyF;QACJxD,iBAAiBC,UAAUC,UAAU,CAACiD,aAAalC;YACjD,IAAI,CAAC7C,QAAQ,CAAC+E,YAAY,EAAE;YAE5B,MAAM/B,OAAOH,YAAYG,IAAI;YAC7B,MAAMxC,QAAQqC,YAAYwC,WAAW;YAErC,MAAMC,WAAW;gBACf9C,MAAM;gBACNO,MACE,CAAC,yBAAyB,EAAEgC,YAAY,UAAU,CAAC,GACnD/E,QAAQ,CAAC+E,YAAY,CAAC3E,IAAI;YAC9B;YAEA,IAAII,SAASd,qBAAqBoC,UAAUtB,QAAQ;oBAO9CR,+BAAAA,uBACEA;gBAPN,iCAAiC;gBACjC,MAAMgE,WAAW/B,GAAGsD,eAAe,CAAC/E;gBACpC,MAAMuC,OAAOnD,mBAAmBY,MAAMyC,OAAO;gBAC7C,MAAM0B,MAAMX,WAAW,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,GAAGA;gBAErC,MAAMxC,UAAUP,QAAQ,CAAC+E,YAAY,CAACxE,OAAO,IACzCP,gCAAAA,CAAAA,wBAAAA,QAAQ,CAAC+E,YAAY,EAACxE,OAAO,qBAA7BP,mCAAAA,uBAAgC2E,OAChC,CAAC,GAAC3E,gCAAAA,QAAQ,CAAC+E,YAAY,CAAC5E,OAAO,qBAA7BH,6BAA+B,CAAC2E,IAAI;gBAE1C,IAAIpE,SAAS;wBAaHP,gCACAA,+BAAAA;oBAbRoF,aAAa;wBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;wBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;wBAChDoB,UAAU;4BACRC,OAAOlF,MAAMmF,QAAQ;4BACrBC,QAAQpF,MAAMqF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BACb;gCACEvD,MAAM;gCACNO,MACE/C,EAAAA,iCAAAA,QAAQ,CAAC+E,YAAY,CAAC5E,OAAO,qBAA7BH,8BAA+B,CAAC2E,IAAI,OACpC3E,gCAAAA,CAAAA,yBAAAA,QAAQ,CAAC+E,YAAY,EAAC9D,OAAO,qBAA7BjB,mCAAAA,wBAAgC2E,SAChC;4BACJ;4BACAW;yBACD;oBACH;gBACF,OAAO;oBACL,qCAAqC;oBACrCF,aAAa;wBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;wBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;wBAChDoB,UAAU;4BACRC,OAAOlF,MAAMmF,QAAQ;4BACrBC,QAAQpF,MAAMqF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BAACT;yBAAS;oBAC3B;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChCF,aAAa;oBACX5C,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;oBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;oBAChDoB,UAAU;wBACRC,OAAO1C,KAAK2C,QAAQ;wBACpBC,QAAQ5C,KAAK6C,QAAQ;oBACvB;oBACAC,cAAc,EAAE;oBAChBC,eAAe;wBACb;4BACEvD,MAAM;4BACNO,MAAMuB,kBAAkBS;wBAC1B;wBACAO;qBACD;gBACH;YACF;QACF;QACA,OAAOF;IACT;IAEA,iDAAiD;IACjDY,2BACEC,SAAiB,EACjBtC,IAAkC;QAElC,MAAM1B,KAAKtC;QACX,IACEgE,QACAA,KAAKE,eAAe,IACpBF,KAAKE,eAAe,CAACI,UAAU,CAAC,oBAChC;YACA,IAAIiC,UAAU;YACd,IAAIvC,KAAKE,eAAe,KAAK,qCAAqC;gBAChEqC,UAAU5B,kBAAkB2B;YAC9B,OAAO;gBACL,MAAM9F,UAAUH,QAAQ,CAAC2D,KAAKC,UAAU,CAAC,CAACzD,OAAO;gBACjD,IAAI,CAACA,SAAS;gBACd+F,UAAU/F,OAAO,CAAC8F,UAAU;YAC9B;YACA,OAAO;gBACLjD,MAAMiD;gBACNzD,MAAMP,GAAGoB,iBAAiB,CAACmC,WAAW;gBACtCjC,eAAetB,GAAGuB,yBAAyB,CAACa,IAAI;gBAChDyB,cAAc,EAAE;gBAChBC,eAAe;oBACb;wBACEvD,MAAM;wBACNO,MAAMmD;oBACR;iBACD;YACH;QACF;IACF;IAEA,yCAAyC;IACzCC,kDACEnE,MAA2B,EAC3BI,IAAgC;QAEhC,MAAMH,KAAKtC;QAEX,MAAMyG,cAAqC,EAAE;QAE7C,yCAAyC;QACzC,IAAInE,GAAGU,yBAAyB,CAACP,KAAKQ,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeT,KAAKQ,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAME,OAAOH,YAAYG,IAAI;gBAC7B,IAAIf,GAAGoE,YAAY,CAACrD,OAAO;oBACzB,IAAI,CAAClD,gBAAgBwG,QAAQ,CAACtD,KAAKD,IAAI,KAAK,CAAC/C,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAAE;wBAChEqD,YAAYpB,IAAI,CAAC;4BACfuB,MAAMvE;4BACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;4BACrCC,MAAM9G,eAAe+G,oBAAoB;4BACzCC,aAAa,CAAC,CAAC,EAAE7D,KAAKD,IAAI,CAAC,4CAA4C,CAAC;4BACxE2C,OAAO1C,KAAK2C,QAAQ;4BACpBC,QAAQ5C,KAAK6C,QAAQ;wBACvB;oBACF,OAAO,IAAI7F,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAMvC,QAAQqC,YAAYwC,WAAW;wBACrC,MAAMlF,UAAUH,QAAQ,CAACgD,KAAKD,IAAI,CAAC,CAAC5C,OAAO;wBAE3C,IAAIK,SAASL,SAAS;4BACpB,IAAI2G,iBAAiB;4BACrB,IAAIC,eAAe;4BACnB,IAAIC,YAAY;4BAEhB,IACE/E,GAAGsD,eAAe,CAAC/E,UACnByB,GAAGgF,+BAA+B,CAACzG,QACnC;oCAQGR,6BAAAA;gCAPH,MAAMkH,MAAM,MAAMtH,mBAAmBY,MAAMyC,OAAO,MAAM;gCACxD,MAAMkE,gBAAgB3C,OAAOS,IAAI,CAAC9E,SAASiH,MAAM,CAAC,CAACrG,IACjD,QAAQsG,IAAI,CAACtG;gCAGf,IACE,CAACoG,cAAcb,QAAQ,CAACY,QACxB,GAAClH,8BAAAA,CAAAA,sBAAAA,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAACxC,OAAO,qBAA3BP,iCAAAA,qBAA8BkH,OAC/B;oCACAF,YAAY;oCACZF,iBAAiBI;gCACnB;4BACF,OAAO,IACLjF,GAAGqF,gBAAgB,CAAC9G,UACnByB,GAAGsF,uBAAuB,CAAC/G,UAC1ByB,GAAGuF,YAAY,CAAC,AAAChH,MAAciH,QAAQ,KACtCxF,CAAAA,GAAGqF,gBAAgB,CAAC,AAAC9G,MAAckH,OAAO,CAAClF,IAAI,KAC7CP,GAAGoE,YAAY,CAAC,AAAC7F,MAAckH,OAAO,CAAClF,IAAI,KAC1C,AAAChC,MAAckH,OAAO,CAAClF,IAAI,CAACS,OAAO,OAAO,UAAU,KACzDhB,GAAGoE,YAAY,CAAC7F,UAAUA,MAAMyC,OAAO,OAAO,YAC/C;oCAEKjD,8BAAAA;gCADL,MAAMe,IAAIP,MAAMyC,OAAO;gCACvB,IAAI,GAACjD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAACxC,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCiG,YAAY;oCACZF,iBAAiB/F;gCACnB;4BACF,OAAO,IACLP,MAAMgC,IAAI,KAAKP,GAAGQ,UAAU,CAACkF,WAAW,IACxCnH,MAAMgC,IAAI,KAAKP,GAAGQ,UAAU,CAACmF,YAAY,EACzC;oCAEK5H,8BAAAA;gCADL,MAAMe,IAAIP,MAAMyC,OAAO;gCACvB,IAAI,GAACjD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAACxC,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCiG,YAAY;oCACZF,iBAAiB/F;gCACnB;4BACF,OAAO,IAAIkB,GAAG4F,wBAAwB,CAACrH,QAAQ;oCAG1CR,8BAAAA;gCAFH,MAAMe,IAAIP,MAAMyC,OAAO;gCACvB,IACE,GAACjD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACgD,KAAKD,IAAI,CAAC,EAACxC,OAAO,qBAA3BP,kCAAAA,sBACCU,KAAKoH,SAAS,CAACtH,MAAMuH,QAAQ,CAACrD,GAAG,CAAC,CAACsD,IAAMA,EAAE/E,OAAO,QAEpD;oCACA+D,YAAY;oCACZF,iBAAiB/F;gCACnB;4BACF,OAAO,IACL,iBAAiB;4BACjBkB,GAAGgG,eAAe,CAACzH,UACnByB,GAAGiG,yBAAyB,CAAC1H,UAC7ByB,GAAGkG,0BAA0B,CAAC3H,UAC9ByB,GAAGsF,uBAAuB,CAAC/G,QAC3B;gCACAwG,YAAY;gCACZF,iBAAiBtG,MAAMyC,OAAO;4BAChC,OAAO;gCACL,8DAA8D;gCAC9D+D,YAAY;gCACZF,iBAAiBtG,MAAMyC,OAAO;gCAC9B8D,eAAe,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAE9D,KAAKD,IAAI,CAAC,0DAA0D,CAAC;4BAC3I;4BAEA,IAAIiE,WAAW;gCACbZ,YAAYpB,IAAI,CAAC;oCACfuB,MAAMvE;oCACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;oCACrCC,MAAM9G,eAAeuI,oBAAoB;oCACzCvB,aACEE,gBACA,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAE9D,KAAKD,IAAI,CAAC,SAAS,CAAC;oCAC3E2C,OAAOlF,MAAMmF,QAAQ;oCACrBC,QAAQpF,MAAMqF,QAAQ;gCACxB;4BACF;wBACF;oBACF,OAAO,IAAI7C,KAAKD,IAAI,KAAKhD,sBAAsB;wBAC7C,gCAAgC;wBAChC,4BAA4B;wBAC5B,MAAMS,QAAQqC,YAAYwC,WAAW;wBACrC,IAAI7E,SAASyB,GAAGiG,yBAAyB,CAAC1H,QAAQ;4BAChD,KAAK,MAAM6H,QAAQ7H,MAAM8H,UAAU,CAAE;gCACnC,IACErG,GAAGsG,oBAAoB,CAACF,SACxBpG,GAAGoE,YAAY,CAACgC,KAAKrF,IAAI,KACzBqF,KAAKrF,IAAI,CAACD,IAAI,KAAK,OACnB;oCACAqD,YAAYpB,IAAI,CAAC;wCACfuB,MAAMvE;wCACNwE,UAAUvE,GAAGwE,kBAAkB,CAACC,KAAK;wCACrCC,MAAM9G,eAAe2I,qBAAqB;wCAC1C3B,aAAa,CAAC,0HAA0H,CAAC;wCACzInB,OAAO2C,KAAK1C,QAAQ;wCACpBC,QAAQyC,KAAKxC,QAAQ;oCACvB;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOO;IACT;AACF;AAEA,eAAexB,OAAM"}