{"version": 3, "sources": ["../../../../src/server/typescript/rules/server.ts"], "names": ["DISALLOWED_SERVER_REACT_APIS", "NEXT_TS_ERRORS", "getTs", "serverLayer", "filterCompletionsAtPosition", "entries", "filter", "e", "includes", "name", "source", "hasDisallowedReactAPIDefinition", "definitions", "some", "d", "containerName", "getSemanticDiagnosticsForImportDeclaration", "node", "ts", "diagnostics", "importPath", "moduleSpecifier", "getText", "importClause", "<PERSON><PERSON><PERSON><PERSON>", "isNamedImports", "elements", "element", "push", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_SERVER_API", "messageText", "start", "getStart", "length", "getWidth"], "mappings": "AAAA,SAASA,4BAA4B,EAAEC,cAAc,QAAQ,cAAa;AAC1E,SAASC,KAAK,QAAQ,WAAU;AAGhC,MAAMC,cAAc;IAClB,6EAA6E;IAC7EC,6BAA4BC,OAAmC;QAC7D,OAAOA,QAAQC,MAAM,CAAC,CAACC;YACrB,gCAAgC;YAChC,IACEP,6BAA6BQ,QAAQ,CAACD,EAAEE,IAAI,KAC5CF,EAAEG,MAAM,KAAK,SACb;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7CC,iCACEC,WAA+C;QAE/C,OAAOA,+BAAAA,YAAaC,IAAI,CACtB,CAACC,IACCd,6BAA6BQ,QAAQ,CAACM,EAAEL,IAAI,KAC5CK,EAAEC,aAAa,KAAK;IAE1B;IAEA,2DAA2D;IAC3DC,4CACEN,MAA2B,EAC3BO,IAAgC;QAEhC,MAAMC,KAAKhB;QAEX,MAAMiB,cAAqC,EAAE;QAE7C,MAAMC,aAAaH,KAAKI,eAAe,CAACC,OAAO,CAACZ;QAChD,IAAIU,eAAe,aAAaA,eAAe,WAAW;YACxD,iCAAiC;YACjC,MAAMG,eAAeN,KAAKM,YAAY;YACtC,IAAIA,cAAc;gBAChB,MAAMC,gBAAgBD,aAAaC,aAAa;gBAChD,IAAIA,iBAAiBN,GAAGO,cAAc,CAACD,gBAAgB;oBACrD,MAAME,WAAWF,cAAcE,QAAQ;oBACvC,KAAK,MAAMC,WAAWD,SAAU;wBAC9B,MAAMjB,OAAOkB,QAAQlB,IAAI,CAACa,OAAO,CAACZ;wBAClC,IAAIV,6BAA6BQ,QAAQ,CAACC,OAAO;4BAC/CU,YAAYS,IAAI,CAAC;gCACfC,MAAMnB;gCACNoB,UAAUZ,GAAGa,kBAAkB,CAACC,KAAK;gCACrCC,MAAMhC,eAAeiC,kBAAkB;gCACvCC,aAAa,CAAC,CAAC,EAAE1B,KAAK,sCAAsC,CAAC;gCAC7D2B,OAAOT,QAAQlB,IAAI,CAAC4B,QAAQ;gCAC5BC,QAAQX,QAAQlB,IAAI,CAAC8B,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOpB;IACT;AACF;AAEA,eAAehB,YAAW"}