{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/fetch-api.ts"], "names": ["handleFetch", "buildRequest", "req", "request", "proxyRequest", "url", "headers", "body", "options", "Request", "Headers", "<PERSON><PERSON><PERSON>", "from", "buildResponse", "response", "UNHANDLED", "ABORT", "CONTINUE", "status", "api", "Array", "arrayBuffer", "toString", "onFetch", "testData"], "mappings": ";;;;+BAmDsBA;;;eAAAA;;;uBAlDqB;AAc3C,SAASC,aAAaC,GAAsB;IAC1C,MAAM,EAAEC,SAASC,YAAY,EAAE,GAAGF;IAClC,MAAM,EAAEG,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGC,SAAS,GAAGJ;IAC3C,OAAO,IAAIK,QAAQJ,KAAK;QACtB,GAAGG,OAAO;QACVF,SAAS,IAAII,QAAQJ;QACrBC,MAAMA,OAAOI,OAAOC,IAAI,CAACL,MAAM,YAAY;IAC7C;AACF;AAEA,eAAeM,cACbC,QAA4B;IAE5B,IAAI,CAACA,UAAU;QACb,OAAOC,gBAAS;IAClB;IACA,IAAID,aAAa,SAAS;QACxB,OAAOE,YAAK;IACd;IACA,IAAIF,aAAa,YAAY;QAC3B,OAAOG,eAAQ;IACjB;IAEA,MAAM,EAAEC,MAAM,EAAEZ,OAAO,EAAEC,IAAI,EAAE,GAAGO;IAClC,OAAO;QACLK,KAAK;QACLL,UAAU;YACRI;YACAZ,SAASc,MAAMR,IAAI,CAACN;YACpBC,MAAMA,OACFI,OAAOC,IAAI,CAAC,MAAME,SAASO,WAAW,IAAIC,QAAQ,CAAC,YACnD;QACN;IACF;AACF;AAEO,eAAetB,YACpBE,GAAsB,EACtBqB,OAAqB;IAErB,MAAM,EAAEC,QAAQ,EAAE,GAAGtB;IACrB,MAAMC,UAAUF,aAAaC;IAC7B,MAAMY,WAAW,MAAMS,QAAQC,UAAUrB;IACzC,OAAOU,cAAcC;AACvB"}