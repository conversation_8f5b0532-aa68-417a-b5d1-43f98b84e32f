-- BHEEMDINE Database Setup
-- Run this in your Supabase SQL editor to create required tables

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create enums
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_status') THEN
        CREATE TYPE tenant_status AS ENUM ('active', 'suspended', 'inactive');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('owner', 'admin', 'manager', 'staff');
    END IF;
END $$;

-- Create tenants table
CREATE TABLE IF NOT EXISTS public.tenants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  country VARCHAR(100) DEFAULT 'US',
  zip_code VARCHAR(20),
  logo_url TEXT,
  website VARCHAR(255),
  description TEXT,
  status tenant_status DEFAULT 'active',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) UNIQUE NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  avatar_url TEXT,
  phone VARCHAR(20),
  timezone VARCHAR(50) DEFAULT 'UTC',
  language VARCHAR(10) DEFAULT 'en',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_tenants table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS public.user_tenants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  role user_role NOT NULL DEFAULT 'staff',
  permissions JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON public.tenants(slug);
CREATE INDEX IF NOT EXISTS idx_tenants_status ON public.tenants(status);
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_user_tenants_user_id ON public.user_tenants(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_tenant_id ON public.user_tenants(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_active ON public.user_tenants(is_active);

-- Enable Row Level Security (RLS)
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_tenants ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Tenants: Users can see tenants they belong to
CREATE POLICY "Users can view their tenants" ON public.tenants
    FOR SELECT USING (
        id IN (
            SELECT tenant_id FROM public.user_tenants 
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

-- Tenants: Owners can update their tenants
CREATE POLICY "Owners can update their tenants" ON public.tenants
    FOR UPDATE USING (
        id IN (
            SELECT tenant_id FROM public.user_tenants 
            WHERE user_id = auth.uid() AND role = 'owner' AND is_active = true
        )
    );

-- Tenants: Allow insert for new tenant creation
CREATE POLICY "Allow tenant creation" ON public.tenants
    FOR INSERT WITH CHECK (true);

-- Users: Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (id = auth.uid());

-- Users: Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (id = auth.uid());

-- Users: Allow insert for new user profiles
CREATE POLICY "Allow user profile creation" ON public.users
    FOR INSERT WITH CHECK (id = auth.uid());

-- User-Tenants: Users can view their tenant associations
CREATE POLICY "Users can view their tenant associations" ON public.user_tenants
    FOR SELECT USING (user_id = auth.uid());

-- User-Tenants: Allow insert for new associations
CREATE POLICY "Allow user-tenant associations" ON public.user_tenants
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- User-Tenants: Owners can manage associations for their tenants
CREATE POLICY "Owners can manage tenant associations" ON public.user_tenants
    FOR ALL USING (
        tenant_id IN (
            SELECT tenant_id FROM public.user_tenants 
            WHERE user_id = auth.uid() AND role = 'owner' AND is_active = true
        )
    );

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS handle_tenants_updated_at ON public.tenants;
CREATE TRIGGER handle_tenants_updated_at
    BEFORE UPDATE ON public.tenants
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_users_updated_at ON public.users;
CREATE TRIGGER handle_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_user_tenants_updated_at ON public.user_tenants;
CREATE TRIGGER handle_user_tenants_updated_at
    BEFORE UPDATE ON public.user_tenants
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();