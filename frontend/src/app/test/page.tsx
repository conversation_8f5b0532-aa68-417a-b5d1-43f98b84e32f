/**
 * Simple test page to verify app is loading
 */

'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';

export default function TestPage() {
  const [status, setStatus] = useState('Loading...');

  useEffect(() => {
    const testConnection = async () => {
      try {
        // Test Supabase connection
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          setStatus(`❌ Auth Error: ${error.message}`);
        } else {
          setStatus('✅ App is working! Supabase connected.');
        }
      } catch (err) {
        setStatus(`❌ Connection Error: ${err}`);
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          BHEEMDINE Test Page
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold mb-2">Connection Status</h2>
          <p className="text-gray-600">{status}</p>
        </div>

        <div className="space-y-2">
          <a
            href="/"
            className="block w-full bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700"
          >
            Go to Home
          </a>
          <a
            href="/login"
            className="block w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Go to Login
          </a>
          <a
            href="/signup"
            className="block w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Go to Signup
          </a>
        </div>

        <div className="mt-6 text-sm text-gray-500">
          <p>Environment: {process.env.NODE_ENV}</p>
          <p>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}</p>
        </div>
      </div>
    </div>
  );
}