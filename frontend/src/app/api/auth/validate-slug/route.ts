/**
 * API route to validate tenant slug availability
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServiceClient } from '@/lib/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const slug = request.nextUrl.searchParams.get('slug');

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      );
    }

    // Validate slug format
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    if (!slugRegex.test(slug) || slug.length < 3 || slug.length > 50) {
      return NextResponse.json(
        { available: false, error: 'Invalid slug format' },
        { status: 400 }
      );
    }

    // Check reserved slugs
    const reservedSlugs = [
      'api', 'admin', 'app', 'www', 'mail', 'ftp', 'localhost',
      'dashboard', 'billing', 'support', 'help', 'docs', 'blog',
      'status', 'staging', 'dev', 'test', 'demo', 'cdn', 'assets',
      'static', 'public', 'private', 'secure', 'internal', 'system',
      'root', 'user', 'account', 'login', 'signup', 'auth', 'oauth',
      'settings', 'profile', 'home', 'index', 'about', 'contact',
      'terms', 'privacy', 'legal', 'pricing', 'features', 'enterprise'
    ];

    if (reservedSlugs.includes(slug.toLowerCase())) {
      return NextResponse.json(
        { available: false, error: 'Slug is reserved' },
        { status: 400 }
      );
    }

    // Check database for existing slug
    const supabase = createSupabaseServiceClient();
    const { data, error } = await supabase
      .from('tenants')
      .select('slug')
      .eq('slug', slug)
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is what we want
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }

    // If data exists, slug is taken
    const available = !data;

    return NextResponse.json({
      available,
      slug,
    });

  } catch (error) {
    console.error('Slug validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}