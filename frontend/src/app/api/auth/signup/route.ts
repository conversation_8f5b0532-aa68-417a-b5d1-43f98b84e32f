/**
 * API route for tenant admin signup
 * Creates tenant, admin user, and returns session
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';
import crypto from 'crypto';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function POST(request: NextRequest) {
  try {
    console.log('Starting signup process...');
    
    const body = await request.json();
    console.log('Received body:', { ...body, password: '[REDACTED]' });
    
    const { businessName, tenantSlug, adminEmail, password } = body;
    
    // Basic validation
    if (!businessName || !tenantSlug || !adminEmail || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'All fields are required',
        },
        { status: 400 }
      );
    }
    
    // Create Supabase service client
    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
    
    console.log('Checking if tenant slug exists...');
    // Check if slug is already taken
    const { data: existingTenant } = await supabase
      .from('tenants')
      .select('slug')
      .eq('slug', tenantSlug)
      .single();

    if (existingTenant) {
      console.log('Tenant slug already exists');
      return NextResponse.json(
        {
          success: false,
          error: 'Tenant slug already exists',
        },
        { status: 400 }
      );
    }
    
    console.log('Creating auth user...');
    // Create Supabase auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: adminEmail,
      password: password,
      email_confirm: true,
    });

    if (authError || !authData.user) {
      console.error('Auth user creation error:', authError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create user account: ' + authError?.message,
        },
        { status: 500 }
      );
    }
    
    console.log('Auth user created:', authData.user.id);
    
    console.log('Creating tenant...');
    // Create tenant with UUID
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        id: crypto.randomUUID(),
        name: businessName,
        slug: tenantSlug,
        email: adminEmail,
        status: 'ACTIVE',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (tenantError || !tenant) {
      console.error('Tenant creation error:', tenantError);
      
      // Clean up auth user if tenant creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create tenant: ' + tenantError?.message,
        },
        { status: 500 }
      );
    }
    
    console.log('Tenant created:', tenant.id);
    
    console.log('Creating user profile...');
    // Create user profile
    const { data: user, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: adminEmail,
        first_name: businessName,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (userError || !user) {
      console.error('User profile creation error:', userError);
      
      // Clean up auth user and tenant
      await supabase.auth.admin.deleteUser(authData.user.id);
      await supabase.from('tenants').delete().eq('id', tenant.id);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create user profile: ' + userError?.message,
        },
        { status: 500 }
      );
    }
    
    console.log('User profile created:', user.id);
    
    console.log('Creating user-tenant association...');
    // Create user-tenant association
    const { error: associationError } = await supabase
      .from('user_tenants')
      .insert({
        id: crypto.randomUUID(),
        user_id: authData.user.id,
        tenant_id: tenant.id,
        role: 'OWNER',
        is_active: true,
      });

    if (associationError) {
      console.error('User-tenant association error:', associationError);
      
      // Clean up created records
      await supabase.auth.admin.deleteUser(authData.user.id);
      await supabase.from('tenants').delete().eq('id', tenant.id);
      await supabase.from('users').delete().eq('id', user.id);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create account association: ' + associationError?.message,
        },
        { status: 500 }
      );
    }
    
    console.log('User-tenant association created');
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Account created successfully',
      data: {
        tenant: {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
        },
        user: {
          id: user.id,
          email: user.email,
        },
      },
    });

  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error: ' + (error as Error).message,
      },
      { status: 500 }
    );
  }
}