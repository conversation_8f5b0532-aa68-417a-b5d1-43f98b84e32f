/**
 * Tenant setup page for completing OAuth signup
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { useSlugify } from '@/hooks/useSlugify';

interface SetupFormData {
  business_name: string;
  tenant_slug: string;
  business_type: string;
  phone: string;
  address: string;
}

export default function SetupPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<SetupFormData>({
    business_name: '',
    tenant_slug: '',
    business_type: 'restaurant',
    phone: '',
    address: '',
  });

  const { generateSlug, validateSlugAvailability, isValidSlugFormat } = useSlugify();

  // Check if user is authenticated
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        router.push('/login');
        return;
      }

      setUser(session.user);
      
      // Pre-fill form with user data if available
      if (session.user.user_metadata) {
        const metadata = session.user.user_metadata;
        setFormData(prev => ({
          ...prev,
          business_name: metadata.full_name || metadata.name || '',
        }));
      }
      
      setLoading(false);
    };

    checkAuth();
  }, [router]);

  // Auto-generate slug from business name
  useEffect(() => {
    if (formData.business_name && !formData.tenant_slug) {
      const slug = generateSlug(formData.business_name);
      setFormData(prev => ({ ...prev, tenant_slug: slug }));
    }
  }, [formData.business_name, generateSlug]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const slug = generateSlug(e.target.value);
    setFormData(prev => ({ ...prev, tenant_slug: slug }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate slug
      if (!isValidSlugFormat(formData.tenant_slug)) {
        throw new Error('Invalid tenant slug. Use only letters, numbers, and hyphens.');
      }

      // Check slug availability
      const isAvailable = await validateSlugAvailability(formData.tenant_slug);
      if (!isAvailable) {
        throw new Error('This business name is already taken. Please choose another.');
      }

      // Create tenant (let database generate ID)
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .insert({
          name: formData.business_name,
          slug: formData.tenant_slug,
          email: user.email,
          phone: formData.phone,
          address: formData.address,
          status: 'active',
          settings: { business_type: formData.business_type },
        })
        .select()
        .single();

      console.log('Tenant creation result:', { tenant, tenantError });

      if (tenantError) throw tenantError;

      // Create user profile if it doesn't exist
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existingUser) {
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: user.id,
            email: user.email,
            first_name: user.user_metadata?.first_name || user.user_metadata?.name || '',
            last_name: user.user_metadata?.last_name || '',
          });

        if (userError) {
          console.warn('User profile creation warning:', userError);
        }
      }

      // Associate user with tenant
      const { error: userTenantError } = await supabase
        .from('user_tenants')
        .insert({
          user_id: user.id,
          tenant_id: tenant.id,
          role: 'owner',
          is_active: true,
        });

      if (userTenantError) throw userTenantError;

      // Redirect to tenant dashboard
      router.push(`/t/${formData.tenant_slug}/dashboard`);

    } catch (err) {
      console.error('Setup error details:', err);
      
      let errorMessage = 'Setup failed. Please try again.';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'object' && err !== null) {
        // Handle Supabase error object
        const supabaseError = err as any;
        if (supabaseError.message) {
          errorMessage = supabaseError.message;
        } else if (supabaseError.details) {
          errorMessage = `Database error: ${supabaseError.details}`;
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Complete Your Setup
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Let's set up your restaurant on TapDine
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Business Name */}
            <div>
              <label htmlFor="business_name" className="block text-sm font-medium text-gray-700">
                Business Name *
              </label>
              <input
                id="business_name"
                name="business_name"
                type="text"
                required
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Your Restaurant Name"
                value={formData.business_name}
                onChange={handleChange}
              />
            </div>

            {/* Tenant Slug */}
            <div>
              <label htmlFor="tenant_slug" className="block text-sm font-medium text-gray-700">
                URL Identifier *
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  tapdine.com/
                </span>
                <input
                  id="tenant_slug"
                  name="tenant_slug"
                  type="text"
                  required
                  className="flex-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-none rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="your-restaurant"
                  value={formData.tenant_slug}
                  onChange={handleSlugChange}
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                This will be your unique URL. Only letters, numbers, and hyphens allowed.
              </p>
            </div>

            {/* Business Type */}
            <div>
              <label htmlFor="business_type" className="block text-sm font-medium text-gray-700">
                Business Type
              </label>
              <select
                id="business_type"
                name="business_type"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.business_type}
                onChange={handleChange}
              >
                <option value="restaurant">Restaurant</option>
                <option value="cafe">Cafe</option>
                <option value="bar">Bar</option>
                <option value="food_truck">Food Truck</option>
                <option value="bakery">Bakery</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                Phone Number
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="+****************"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>

            {/* Address */}
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                Address
              </label>
              <input
                id="address"
                name="address"
                type="text"
                className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="123 Main St, City, State 12345"
                value={formData.address}
                onChange={handleChange}
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : null}
                Complete Setup
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
