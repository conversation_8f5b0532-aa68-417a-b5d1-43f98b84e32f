(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[155],{9494:function(e,t,r){"use strict";r.r(t),r.d(t,{Headers:function(){return n},Request:function(){return o},Response:function(){return a},fetch:function(){return i}});var s=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let i=s.fetch;t.default=s.fetch.bind(s);let n=s.Headers,o=s.Request,a=s.Response},9430:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=s(r(9494)),n=s(r(6711));let PostgrestBuilder=class PostgrestBuilder{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=this.fetch,s=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,s;let i=null,o=null,a=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(o="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),n=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&n&&n.length>1&&(a=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(i={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,l=406,u="Not Acceptable"):o=1===o.length?o[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(o=[],i=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null===(s=null==i?void 0:i.details)||void 0===s?void 0:s.includes("0 rows"))&&(i=null,l=200,u="OK"),i&&this.shouldThrowOnError)throw new n.default(i)}let h={error:i,data:o,count:a,status:l,statusText:u};return h});return this.shouldThrowOnError||(s=s.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}};t.default=PostgrestBuilder},8551:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=s(r(807)),n=s(r(4384)),o=r(9085);let PostgrestClient=class PostgrestClient{constructor(e,{headers:t={},schema:r,fetch:s}={}){this.url=e,this.headers=Object.assign(Object.assign({},o.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=s}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new PostgrestClient(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:s=!1,count:i}={}){let o,a;let l=new URL(`${this.url}/rpc/${e}`);r||s?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(o="POST",a=t);let u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new n.default({method:o,url:l,headers:u,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};t.default=PostgrestClient},6711:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});let PostgrestError=class PostgrestError extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};t.default=PostgrestError},4384:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=s(r(1483));let PostgrestFilterBuilder=class PostgrestFilterBuilder extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let i="";"plain"===s?i="pl":"phrase"===s?i="ph":"websearch"===s&&(i="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}};t.default=PostgrestFilterBuilder},807:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=s(r(4384));let PostgrestQueryBuilder=class PostgrestQueryBuilder{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:t=!1,count:r}={}){let s=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!s?"":('"'===e&&(s=!s),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:s,defaultToNull:n=!0}={}){let o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),s&&o.push(`count=${s}`),n||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};t.default=PostgrestQueryBuilder},1483:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=s(r(9430));let PostgrestTransformBuilder=class PostgrestTransformBuilder extends i.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){let n=i?`${i}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){let i=void 0===s?"offset":`${s}.offset`,n=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:n="text"}={}){var o;let a=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};t.default=PostgrestTransformBuilder},9085:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let s=r(7687);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${s.version}`}},4830:function(e,t,r){"use strict";var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=s(r(8551));t.PostgrestClient=i.default;let n=s(r(807));t.PostgrestQueryBuilder=n.default;let o=s(r(4384));t.PostgrestFilterBuilder=o.default;let a=s(r(1483));t.PostgrestTransformBuilder=a.default;let l=s(r(9430));t.PostgrestBuilder=l.default;let u=s(r(6711));t.PostgrestError=u.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:l.default,PostgrestError:u.default}},7687:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},4482:function(e,t){"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.Q=parse,t.q=serialize;var r=Object.prototype.toString,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function parse(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=(t||{}).decode||decode,i=0;i<e.length;){var n=e.indexOf("=",i);if(-1===n)break;var o=e.indexOf(";",i);if(-1===o)o=e.length;else if(o<n){i=e.lastIndexOf(";",n-1)+1;continue}var a=e.slice(i,n).trim();if(void 0===r[a]){var l=e.slice(n+1,o).trim();34===l.charCodeAt(0)&&(l=l.slice(1,-1)),r[a]=tryDecode(l,s)}i=o+1}return r}function serialize(e,t,r){var i=r||{},n=i.encode||encode;if("function"!=typeof n)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=n(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var a=e+"="+o;if(null!=i.maxAge){var l=i.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");a+="; Max-Age="+Math.floor(l)}if(i.domain){if(!s.test(i.domain))throw TypeError("option domain is invalid");a+="; Domain="+i.domain}if(i.path){if(!s.test(i.path))throw TypeError("option path is invalid");a+="; Path="+i.path}if(i.expires){var u=i.expires;if(!isDate(u)||isNaN(u.valueOf()))throw TypeError("option expires is invalid");a+="; Expires="+u.toUTCString()}if(i.httpOnly&&(a+="; HttpOnly"),i.secure&&(a+="; Secure"),i.priority)switch("string"==typeof i.priority?i.priority.toLowerCase():i.priority){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return a}function decode(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function encode(e){return encodeURIComponent(e)}function isDate(e){return"[object Date]"===r.call(e)||e instanceof Date}function tryDecode(e,t){try{return t(e)}catch(t){return e}}},263:function(e){!function(){var t={675:function(e,t){"use strict";t.byteLength=byteLength,t.toByteArray=toByteArray,t.fromByteArray=fromByteArray;for(var r=[],s=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=n.length;o<a;++o)r[o]=n[o],s[n.charCodeAt(o)]=o;function getLens(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var s=r===t?0:4-r%4;return[r,s]}function byteLength(e){var t=getLens(e),r=t[0],s=t[1];return(r+s)*3/4-s}function _byteLength(e,t,r){return(t+r)*3/4-r}function toByteArray(e){var t,r,n=getLens(e),o=n[0],a=n[1],l=new i(_byteLength(e,o,a)),u=0,h=a>0?o-4:o;for(r=0;r<h;r+=4)t=s[e.charCodeAt(r)]<<18|s[e.charCodeAt(r+1)]<<12|s[e.charCodeAt(r+2)]<<6|s[e.charCodeAt(r+3)],l[u++]=t>>16&255,l[u++]=t>>8&255,l[u++]=255&t;return 2===a&&(t=s[e.charCodeAt(r)]<<2|s[e.charCodeAt(r+1)]>>4,l[u++]=255&t),1===a&&(t=s[e.charCodeAt(r)]<<10|s[e.charCodeAt(r+1)]<<4|s[e.charCodeAt(r+2)]>>2,l[u++]=t>>8&255,l[u++]=255&t),l}function tripletToBase64(e){return r[e>>18&63]+r[e>>12&63]+r[e>>6&63]+r[63&e]}function encodeChunk(e,t,r){for(var s=[],i=t;i<r;i+=3)s.push(tripletToBase64((e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2])));return s.join("")}function fromByteArray(e){for(var t,s=e.length,i=s%3,n=[],o=0,a=s-i;o<a;o+=16383)n.push(encodeChunk(e,o,o+16383>a?a:o+16383));return 1===i?n.push(r[(t=e[s-1])>>2]+r[t<<4&63]+"=="):2===i&&n.push(r[(t=(e[s-2]<<8)+e[s-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),n.join("")}s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63},72:function(e,t,r){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var s=r(675),i=r(783),n="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function typedArraySupport(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}function createBuffer(e){if(e>**********)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,Buffer.prototype),t}function Buffer(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return allocUnsafe(e)}return from(e,t,r)}function from(e,t,r){if("string"==typeof e)return fromString(e,t);if(ArrayBuffer.isView(e))return fromArrayLike(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(isInstance(e,ArrayBuffer)||e&&isInstance(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(isInstance(e,SharedArrayBuffer)||e&&isInstance(e.buffer,SharedArrayBuffer)))return fromArrayBuffer(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return Buffer.from(s,t,r);var i=fromObject(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return Buffer.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function assertSize(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function alloc(e,t,r){return(assertSize(e),e<=0)?createBuffer(e):void 0!==t?"string"==typeof r?createBuffer(e).fill(t,r):createBuffer(e).fill(t):createBuffer(e)}function allocUnsafe(e){return assertSize(e),createBuffer(e<0?0:0|checked(e))}function fromString(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!Buffer.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|byteLength(e,t),s=createBuffer(r),i=s.write(e,t);return i!==r&&(s=s.slice(0,i)),s}function fromArrayLike(e){for(var t=e.length<0?0:0|checked(e.length),r=createBuffer(t),s=0;s<t;s+=1)r[s]=255&e[s];return r}function fromArrayBuffer(e,t,r){var s;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),Buffer.prototype),s}function fromObject(e){if(Buffer.isBuffer(e)){var t,r=0|checked(e.length),s=createBuffer(r);return 0===s.length||e.copy(s,0,0,r),s}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?createBuffer(0):fromArrayLike(e):"Buffer"===e.type&&Array.isArray(e.data)?fromArrayLike(e.data):void 0}function checked(e){if(e>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function SlowBuffer(e){return+e!=e&&(e=0),Buffer.alloc(+e)}function byteLength(e,t){if(Buffer.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||isInstance(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return utf8ToBytes(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return base64ToBytes(e).length;default:if(i)return s?-1:utf8ToBytes(e).length;t=(""+t).toLowerCase(),i=!0}}function slowToString(e,t,r){var s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return hexSlice(this,t,r);case"utf8":case"utf-8":return utf8Slice(this,t,r);case"ascii":return asciiSlice(this,t,r);case"latin1":case"binary":return latin1Slice(this,t,r);case"base64":return base64Slice(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return utf16leSlice(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function swap(e,t,r){var s=e[t];e[t]=e[r],e[r]=s}function bidirectionalIndexOf(e,t,r,s,i){var n;if(0===e.length)return -1;if("string"==typeof r?(s=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),(n=r=+r)!=n&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof t&&(t=Buffer.from(t,s)),Buffer.isBuffer(t))return 0===t.length?-1:arrayIndexOf(e,t,r,s,i);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):arrayIndexOf(e,[t],r,s,i);throw TypeError("val must be string, number or Buffer")}function arrayIndexOf(e,t,r,s,i){var n,o=1,a=e.length,l=t.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||t.length<2)return -1;o=2,a/=2,l/=2,r/=2}function read(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var u=-1;for(n=r;n<a;n++)if(read(e,n)===read(t,-1===u?0:n-u)){if(-1===u&&(u=n),n-u+1===l)return u*o}else -1!==u&&(n-=n-u),u=-1}else for(r+l>a&&(r=a-l),n=r;n>=0;n--){for(var h=!0,c=0;c<l;c++)if(read(e,n+c)!==read(t,c)){h=!1;break}if(h)return n}return -1}function hexWrite(e,t,r,s){r=Number(r)||0;var i=e.length-r;s?(s=Number(s))>i&&(s=i):s=i;var n=t.length;s>n/2&&(s=n/2);for(var o=0;o<s;++o){var a=parseInt(t.substr(2*o,2),16);if(a!=a)break;e[r+o]=a}return o}function utf8Write(e,t,r,s){return blitBuffer(utf8ToBytes(t,e.length-r),e,r,s)}function latin1Write(e,t,r,s){return blitBuffer(asciiToBytes(t),e,r,s)}function base64Write(e,t,r,s){return blitBuffer(base64ToBytes(t),e,r,s)}function ucs2Write(e,t,r,s){return blitBuffer(utf16leToBytes(t,e.length-r),e,r,s)}function base64Slice(e,t,r){return 0===t&&r===e.length?s.fromByteArray(e):s.fromByteArray(e.slice(t,r))}function utf8Slice(e,t,r){r=Math.min(e.length,r);for(var s=[],i=t;i<r;){var n,o,a,l,u=e[i],h=null,c=u>239?4:u>223?3:u>191?2:1;if(i+c<=r)switch(c){case 1:u<128&&(h=u);break;case 2:(192&(n=e[i+1]))==128&&(l=(31&u)<<6|63&n)>127&&(h=l);break;case 3:n=e[i+1],o=e[i+2],(192&n)==128&&(192&o)==128&&(l=(15&u)<<12|(63&n)<<6|63&o)>2047&&(l<55296||l>57343)&&(h=l);break;case 4:n=e[i+1],o=e[i+2],a=e[i+3],(192&n)==128&&(192&o)==128&&(192&a)==128&&(l=(15&u)<<18|(63&n)<<12|(63&o)<<6|63&a)>65535&&l<1114112&&(h=l)}null===h?(h=65533,c=1):h>65535&&(h-=65536,s.push(h>>>10&1023|55296),h=56320|1023&h),s.push(h),i+=c}return decodeCodePointsArray(s)}function decodeCodePointsArray(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",s=0;s<t;)r+=String.fromCharCode.apply(String,e.slice(s,s+=4096));return r}function asciiSlice(e,t,r){var s="";r=Math.min(e.length,r);for(var i=t;i<r;++i)s+=String.fromCharCode(127&e[i]);return s}function latin1Slice(e,t,r){var s="";r=Math.min(e.length,r);for(var i=t;i<r;++i)s+=String.fromCharCode(e[i]);return s}function hexSlice(e,t,r){var s=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>s)&&(r=s);for(var i="",n=t;n<r;++n)i+=a[e[n]];return i}function utf16leSlice(e,t,r){for(var s=e.slice(t,r),i="",n=0;n<s.length;n+=2)i+=String.fromCharCode(s[n]+256*s[n+1]);return i}function checkOffset(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function checkInt(e,t,r,s,i,n){if(!Buffer.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<n)throw RangeError('"value" argument is out of bounds');if(r+s>e.length)throw RangeError("Index out of range")}function checkIEEE754(e,t,r,s,i,n){if(r+s>e.length||r<0)throw RangeError("Index out of range")}function writeFloat(e,t,r,s,n){return t=+t,r>>>=0,n||checkIEEE754(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,s,23,4),r+4}function writeDouble(e,t,r,s,n){return t=+t,r>>>=0,n||checkIEEE754(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,s,52,8),r+8}t.Buffer=Buffer,t.SlowBuffer=SlowBuffer,t.INSPECT_MAX_BYTES=50,t.kMaxLength=**********,Buffer.TYPED_ARRAY_SUPPORT=typedArraySupport(),Buffer.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}}),Buffer.poolSize=8192,Buffer.from=function(e,t,r){return from(e,t,r)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array),Buffer.alloc=function(e,t,r){return alloc(e,t,r)},Buffer.allocUnsafe=function(e){return allocUnsafe(e)},Buffer.allocUnsafeSlow=function(e){return allocUnsafe(e)},Buffer.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==Buffer.prototype},Buffer.compare=function(e,t){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),isInstance(t,Uint8Array)&&(t=Buffer.from(t,t.offset,t.byteLength)),!Buffer.isBuffer(e)||!Buffer.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,s=t.length,i=0,n=Math.min(r,s);i<n;++i)if(e[i]!==t[i]){r=e[i],s=t[i];break}return r<s?-1:s<r?1:0},Buffer.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return Buffer.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,s=Buffer.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var n=e[r];if(isInstance(n,Uint8Array)&&(n=Buffer.from(n)),!Buffer.isBuffer(n))throw TypeError('"list" argument must be an Array of Buffers');n.copy(s,i),i+=n.length}return s},Buffer.byteLength=byteLength,Buffer.prototype._isBuffer=!0,Buffer.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)swap(this,t,t+1);return this},Buffer.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)swap(this,t,t+3),swap(this,t+1,t+2);return this},Buffer.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)swap(this,t,t+7),swap(this,t+1,t+6),swap(this,t+2,t+5),swap(this,t+3,t+4);return this},Buffer.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?utf8Slice(this,0,e):slowToString.apply(this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function(e){if(!Buffer.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===Buffer.compare(this,e)},Buffer.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},n&&(Buffer.prototype[n]=Buffer.prototype.inspect),Buffer.prototype.compare=function(e,t,r,s,i){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),!Buffer.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===s&&(s=0),void 0===i&&(i=this.length),t<0||r>e.length||s<0||i>this.length)throw RangeError("out of range index");if(s>=i&&t>=r)return 0;if(s>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,s>>>=0,i>>>=0,this===e)return 0;for(var n=i-s,o=r-t,a=Math.min(n,o),l=this.slice(s,i),u=e.slice(t,r),h=0;h<a;++h)if(l[h]!==u[h]){n=l[h],o=u[h];break}return n<o?-1:o<n?1:0},Buffer.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},Buffer.prototype.indexOf=function(e,t,r){return bidirectionalIndexOf(this,e,t,r,!0)},Buffer.prototype.lastIndexOf=function(e,t,r){return bidirectionalIndexOf(this,e,t,r,!1)},Buffer.prototype.write=function(e,t,r,s){if(void 0===t)s="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)s=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===s&&(s="utf8")):(s=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,n,o=this.length-t;if((void 0===r||r>o)&&(r=o),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");for(var a=!1;;)switch(s){case"hex":return hexWrite(this,e,t,r);case"utf8":case"utf-8":return utf8Write(this,e,t,r);case"ascii":return i=t,n=r,blitBuffer(asciiToBytes(e),this,i,n);case"latin1":case"binary":return latin1Write(this,e,t,r);case"base64":return base64Write(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ucs2Write(this,e,t,r);default:if(a)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),a=!0}},Buffer.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},Buffer.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var s=this.subarray(e,t);return Object.setPrototypeOf(s,Buffer.prototype),s},Buffer.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||checkOffset(e,t,this.length);for(var s=this[e],i=1,n=0;++n<t&&(i*=256);)s+=this[e+n]*i;return s},Buffer.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||checkOffset(e,t,this.length);for(var s=this[e+--t],i=1;t>0&&(i*=256);)s+=this[e+--t]*i;return s},Buffer.prototype.readUInt8=function(e,t){return e>>>=0,t||checkOffset(e,1,this.length),this[e]},Buffer.prototype.readUInt16LE=function(e,t){return e>>>=0,t||checkOffset(e,2,this.length),this[e]|this[e+1]<<8},Buffer.prototype.readUInt16BE=function(e,t){return e>>>=0,t||checkOffset(e,2,this.length),this[e]<<8|this[e+1]},Buffer.prototype.readUInt32LE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},Buffer.prototype.readUInt32BE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},Buffer.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||checkOffset(e,t,this.length);for(var s=this[e],i=1,n=0;++n<t&&(i*=256);)s+=this[e+n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},Buffer.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||checkOffset(e,t,this.length);for(var s=t,i=1,n=this[e+--s];s>0&&(i*=256);)n+=this[e+--s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},Buffer.prototype.readInt8=function(e,t){return(e>>>=0,t||checkOffset(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},Buffer.prototype.readInt16LE=function(e,t){e>>>=0,t||checkOffset(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},Buffer.prototype.readInt16BE=function(e,t){e>>>=0,t||checkOffset(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},Buffer.prototype.readInt32LE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},Buffer.prototype.readInt32BE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},Buffer.prototype.readFloatLE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),i.read(this,e,!0,23,4)},Buffer.prototype.readFloatBE=function(e,t){return e>>>=0,t||checkOffset(e,4,this.length),i.read(this,e,!1,23,4)},Buffer.prototype.readDoubleLE=function(e,t){return e>>>=0,t||checkOffset(e,8,this.length),i.read(this,e,!0,52,8)},Buffer.prototype.readDoubleBE=function(e,t){return e>>>=0,t||checkOffset(e,8,this.length),i.read(this,e,!1,52,8)},Buffer.prototype.writeUIntLE=function(e,t,r,s){if(e=+e,t>>>=0,r>>>=0,!s){var i=Math.pow(2,8*r)-1;checkInt(this,e,t,r,i,0)}var n=1,o=0;for(this[t]=255&e;++o<r&&(n*=256);)this[t+o]=e/n&255;return t+r},Buffer.prototype.writeUIntBE=function(e,t,r,s){if(e=+e,t>>>=0,r>>>=0,!s){var i=Math.pow(2,8*r)-1;checkInt(this,e,t,r,i,0)}var n=r-1,o=1;for(this[t+n]=255&e;--n>=0&&(o*=256);)this[t+n]=e/o&255;return t+r},Buffer.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,1,255,0),this[t]=255&e,t+1},Buffer.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},Buffer.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},Buffer.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},Buffer.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},Buffer.prototype.writeIntLE=function(e,t,r,s){if(e=+e,t>>>=0,!s){var i=Math.pow(2,8*r-1);checkInt(this,e,t,r,i-1,-i)}var n=0,o=1,a=0;for(this[t]=255&e;++n<r&&(o*=256);)e<0&&0===a&&0!==this[t+n-1]&&(a=1),this[t+n]=(e/o>>0)-a&255;return t+r},Buffer.prototype.writeIntBE=function(e,t,r,s){if(e=+e,t>>>=0,!s){var i=Math.pow(2,8*r-1);checkInt(this,e,t,r,i-1,-i)}var n=r-1,o=1,a=0;for(this[t+n]=255&e;--n>=0&&(o*=256);)e<0&&0===a&&0!==this[t+n+1]&&(a=1),this[t+n]=(e/o>>0)-a&255;return t+r},Buffer.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},Buffer.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},Buffer.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},Buffer.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,4,**********,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},Buffer.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||checkInt(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},Buffer.prototype.writeFloatLE=function(e,t,r){return writeFloat(this,e,t,!0,r)},Buffer.prototype.writeFloatBE=function(e,t,r){return writeFloat(this,e,t,!1,r)},Buffer.prototype.writeDoubleLE=function(e,t,r){return writeDouble(this,e,t,!0,r)},Buffer.prototype.writeDoubleBE=function(e,t,r){return writeDouble(this,e,t,!1,r)},Buffer.prototype.copy=function(e,t,r,s){if(!Buffer.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),s||0===s||(s=this.length),t>=e.length&&(t=e.length),t||(t=0),s>0&&s<r&&(s=r),s===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-t<s-r&&(s=e.length-t+r);var i=s-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,s);else if(this===e&&r<t&&t<s)for(var n=i-1;n>=0;--n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,s),t);return i},Buffer.prototype.fill=function(e,t,r,s){if("string"==typeof e){if("string"==typeof t?(s=t,t=0,r=this.length):"string"==typeof r&&(s=r,r=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!Buffer.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){var i,n=e.charCodeAt(0);("utf8"===s&&n<128||"latin1"===s)&&(e=n)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=Buffer.isBuffer(e)?e:Buffer.from(e,s),a=o.length;if(0===a)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%a]}return this};var o=/[^+/0-9A-Za-z-_]/g;function base64clean(e){if((e=(e=e.split("=")[0]).trim().replace(o,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function utf8ToBytes(e,t){t=t||1/0;for(var r,s=e.length,i=null,n=[],o=0;o<s;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===s){(t-=3)>-1&&n.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&n.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&n.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;n.push(r)}else if(r<2048){if((t-=2)<0)break;n.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;n.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;n.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return n}function asciiToBytes(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function utf16leToBytes(e,t){for(var r,s,i=[],n=0;n<e.length&&!((t-=2)<0);++n)s=(r=e.charCodeAt(n))>>8,i.push(r%256),i.push(s);return i}function base64ToBytes(e){return s.toByteArray(base64clean(e))}function blitBuffer(e,t,r,s){for(var i=0;i<s&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function isInstance(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var a=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var s=16*r,i=0;i<16;++i)t[s+i]=e[r]+e[i];return t}()},783:function(e,t){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */t.read=function(e,t,r,s,i){var n,o,a=8*i-s-1,l=(1<<a)-1,u=l>>1,h=-7,c=r?i-1:0,d=r?-1:1,f=e[t+c];for(c+=d,n=f&(1<<-h)-1,f>>=-h,h+=a;h>0;n=256*n+e[t+c],c+=d,h-=8);for(o=n&(1<<-h)-1,n>>=-h,h+=s;h>0;o=256*o+e[t+c],c+=d,h-=8);if(0===n)n=1-u;else{if(n===l)return o?NaN:(f?-1:1)*(1/0);o+=Math.pow(2,s),n-=u}return(f?-1:1)*o*Math.pow(2,n-s)},t.write=function(e,t,r,s,i,n){var o,a,l,u=8*n-i-1,h=(1<<u)-1,c=h>>1,d=23===i?5960464477539062e-23:0,f=s?0:n-1,p=s?1:-1,g=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,o=h):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+c>=1?t+=d/l:t+=d*Math.pow(2,1-c),t*l>=2&&(o++,l/=2),o+c>=h?(a=0,o=h):o+c>=1?(a=(t*l-1)*Math.pow(2,i),o+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,i),o=0));i>=8;e[r+f]=255&a,f+=p,a/=256,i-=8);for(o=o<<i|a,u+=i;u>0;e[r+f]=255&o,f+=p,o/=256,u-=8);e[r+f-p]|=128*g}}},r={};function __nccwpck_require__(e){var s=r[e];if(void 0!==s)return s.exports;var i=r[e]={exports:{}},n=!0;try{t[e](i,i.exports,__nccwpck_require__),n=!1}finally{n&&delete r[e]}return i.exports}__nccwpck_require__.ab="//";var s=__nccwpck_require__(72);e.exports=s}()},622:function(e,t,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=r(2265),i=Symbol.for("react.element"),n=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,a=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var s,n={},u=null,h=null;for(s in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(h=t.ref),t)o.call(t,s)&&!l.hasOwnProperty(s)&&(n[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===n[s]&&(n[s]=t[s]);return{$$typeof:i,type:e,key:u,ref:h,props:n,_owner:a.current}}t.Fragment=n,t.jsx=q,t.jsxs=q},7437:function(e,t,r){"use strict";e.exports=r(622)},4243:function(e,t,r){"use strict";r.d(t,{AY:function(){return createBrowserClient}});let resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};let FunctionsError=class FunctionsError extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}};let FunctionsFetchError=class FunctionsFetchError extends FunctionsError{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}};let FunctionsRelayError=class FunctionsRelayError extends FunctionsError{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}};let FunctionsHttpError=class FunctionsHttpError extends FunctionsError{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}};(d=f||(f={})).Any="any",d.ApNortheast1="ap-northeast-1",d.ApNortheast2="ap-northeast-2",d.ApSouth1="ap-south-1",d.ApSoutheast1="ap-southeast-1",d.ApSoutheast2="ap-southeast-2",d.CaCentral1="ca-central-1",d.EuCentral1="eu-central-1",d.EuWest1="eu-west-1",d.EuWest2="eu-west-2",d.EuWest3="eu-west-3",d.SaEast1="sa-east-1",d.UsEast1="us-east-1",d.UsWest1="us-west-1",d.UsWest2="us-west-2";var s,i,n,o,a,l,u,h,c,d,f,p,g,y,v,m,w,_,b,k,E,S,__awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let FunctionsClient=class FunctionsClient{constructor(e,{headers:t={},customFetch:r,region:s=f.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=resolveFetch(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return __awaiter(this,void 0,void 0,function*(){try{let s;let{headers:i,method:n,body:o}=t,a={},{region:l}=t;l||(l=this.region);let u=new URL(`${this.url}/${e}`);l&&"any"!==l&&(a["x-region"]=l,u.searchParams.set("forceFunctionRegion",l)),o&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",s=o):"string"==typeof o?(a["Content-Type"]="text/plain",s=o):"undefined"!=typeof FormData&&o instanceof FormData?s=o:(a["Content-Type"]="application/json",s=JSON.stringify(o)));let h=yield this.fetch(u.toString(),{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),i),body:s}).catch(e=>{throw new FunctionsFetchError(e)}),c=h.headers.get("x-relay-error");if(c&&"true"===c)throw new FunctionsRelayError(h);if(!h.ok)throw new FunctionsHttpError(h);let d=(null!==(r=h.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield h.json():"application/octet-stream"===d?yield h.blob():"text/event-stream"===d?h:"multipart/form-data"===d?yield h.formData():yield h.text(),error:null,response:h}}catch(e){return{data:null,error:e,response:e instanceof FunctionsHttpError||e instanceof FunctionsRelayError?e.context:void 0}}})}};var A=r(4830);let{PostgrestClient:T,PostgrestQueryBuilder:j,PostgrestFilterBuilder:P,PostgrestTransformBuilder:C,PostgrestBuilder:O,PostgrestError:B}=A;function getNativeWebSocket(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw Error("`WebSocket` is not supported in this environment")}let R=getNativeWebSocket();(s=p||(p={}))[s.connecting=0]="connecting",s[s.open=1]="open",s[s.closing=2]="closing",s[s.closed=3]="closed",(i=g||(g={})).closed="closed",i.errored="errored",i.joined="joined",i.joining="joining",i.leaving="leaving",(n=y||(y={})).close="phx_close",n.error="phx_error",n.join="phx_join",n.reply="phx_reply",n.leave="phx_leave",n.access_token="access_token",(v||(v={})).websocket="websocket",(o=m||(m={})).Connecting="connecting",o.Open="open",o.Closing="closing",o.Closed="closed";let Serializer=class Serializer{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let s=t.getUint8(1),i=t.getUint8(2),n=this.HEADER_LENGTH+2,o=r.decode(e.slice(n,n+s));n+=s;let a=r.decode(e.slice(n,n+i));n+=i;let l=JSON.parse(r.decode(e.slice(n,e.byteLength)));return{ref:null,topic:o,event:a,payload:l}}};let Timer=class Timer{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}};(a=w||(w={})).abstime="abstime",a.bool="bool",a.date="date",a.daterange="daterange",a.float4="float4",a.float8="float8",a.int2="int2",a.int4="int4",a.int4range="int4range",a.int8="int8",a.int8range="int8range",a.json="json",a.jsonb="jsonb",a.money="money",a.numeric="numeric",a.oid="oid",a.reltime="reltime",a.text="text",a.time="time",a.timestamp="timestamp",a.timestamptz="timestamptz",a.timetz="timetz",a.tsrange="tsrange",a.tstzrange="tstzrange";let convertChangeData=(e,t,r={})=>{var s;let i=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=convertColumn(s,e,t,i),r),{})},convertColumn=(e,t,r,s)=>{let i=t.find(t=>t.name===e),n=null==i?void 0:i.type,o=r[e];return n&&!s.includes(n)?convertCell(n,o):noop(o)},convertCell=(e,t)=>{if("_"===e.charAt(0)){let r=e.slice(1,e.length);return toArray(t,r)}switch(e){case w.bool:return toBoolean(t);case w.float4:case w.float8:case w.int2:case w.int4:case w.int8:case w.numeric:case w.oid:return toNumber(t);case w.json:case w.jsonb:return toJson(t);case w.timestamp:return toTimestampString(t);case w.abstime:case w.date:case w.daterange:case w.int4range:case w.int8range:case w.money:case w.reltime:case w.text:case w.time:case w.timestamptz:case w.timetz:case w.tsrange:case w.tstzrange:default:return noop(t)}},noop=e=>e,toBoolean=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},toNumber=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},toJson=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},toArray=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,s=e[r],i=e[0];if("{"===i&&"}"===s){let s;let i=e.slice(1,r);try{s=JSON.parse("["+i+"]")}catch(e){s=i?i.split(","):[]}return s.map(e=>convertCell(t,e))}return e},toTimestampString=e=>"string"==typeof e?e.replace(" ","T"):e,httpEndpointURL=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};let Push=class Push{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}};(l=_||(_={})).SYNC="sync",l.JOIN="join",l.LEAVE="leave";let RealtimePresence=class RealtimePresence{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=RealtimePresence.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=RealtimePresence.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=RealtimePresence.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){let i=this.cloneDeep(e),n=this.transformState(t),o={},a={};return this.map(i,(e,t)=>{n[e]||(a[e]=t)}),this.map(n,(e,t)=>{let r=i[e];if(r){let s=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),n=t.filter(e=>0>i.indexOf(e.presence_ref)),l=r.filter(e=>0>s.indexOf(e.presence_ref));n.length>0&&(o[e]=n),l.length>0&&(a[e]=l)}else o[e]=t}),this.syncDiff(i,{joins:o,leaves:a},r,s)}static syncDiff(e,t,r,s){let{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(t,s)=>{var i;let n=null!==(i=e[t])&&void 0!==i?i:[];if(e[t]=this.cloneDeep(s),n.length>0){let r=e[t].map(e=>e.presence_ref),s=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...s)}r(t,n,s)}),this.map(n,(t,r)=>{let i=e[t];if(!i)return;let n=r.map(e=>e.presence_ref);i=i.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=i,s(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let s=e[r];return"metas"in s?t[r]=s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}};(u=b||(b={})).ALL="*",u.INSERT="INSERT",u.UPDATE="UPDATE",u.DELETE="DELETE",(h=k||(k={})).BROADCAST="broadcast",h.PRESENCE="presence",h.POSTGRES_CHANGES="postgres_changes",h.SYSTEM="system",(c=E||(E={})).SUBSCRIBED="SUBSCRIBED",c.TIMED_OUT="TIMED_OUT",c.CLOSED="CLOSED",c.CHANNEL_ERROR="CHANNEL_ERROR";let RealtimeChannel=class RealtimeChannel{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=g.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Push(this,y.join,this.params,this.timeout),this.rejoinTimer=new Timer(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=g.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=g.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=g.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=g.errored,this.rejoinTimer.scheduleTimeout())}),this._on(y.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new RealtimePresence(this),this.broadcastEndpointURL=httpEndpointURL(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.state==g.closed){let{config:{broadcast:i,presence:n,private:o}}=this.params;this._onError(t=>null==e?void 0:e(E.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(E.CLOSED));let a={},l={broadcast:i,presence:n,postgres_changes:null!==(s=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==s?s:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(E.SUBSCRIBED);return}{let s=this.bindings.postgres_changes,i=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,n=[];for(let r=0;r<i;r++){let i=s[r],{filter:{event:o,schema:a,table:l,filter:u}}=i,h=t&&t[r];if(h&&h.event===o&&h.schema===a&&h.table===l&&h.filter===u)n.push(Object.assign(Object.assign({},i),{id:h.id}));else{this.unsubscribe(),this.state=g.errored,null==e||e(E.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(E.SUBSCRIBED);return}}).receive("error",t=>{this.state=g.errored,null==e||e(E.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(E.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,i,n;let o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(i=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===i?void 0:i.broadcast)||void 0===n?void 0:n.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{let{event:i,payload:n}=e,o=this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",a={method:"POST",headers:{Authorization:o,apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(s=e.body)||void 0===s?void 0:s.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=g.leaving;let onClose=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(y.close,"leave",this._joinRef())};this.joinPush.destroy();let t=null;return new Promise(r=>{(t=new Push(this,y.leave,{},e)).receive("ok",()=>{onClose(),r("ok")}).receive("timeout",()=>{onClose(),r("timed out")}).receive("error",()=>{r("error")}),t.send(),this._canPush()||t.trigger("ok",{})}).finally(()=>{null==t||t.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let s=new AbortController,i=setTimeout(()=>s.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:s.signal}));return clearTimeout(i),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Push(this,e,t,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,i;let n=e.toLocaleLowerCase(),{close:o,error:a,leave:l,join:u}=y;if(r&&[o,a,l,u].indexOf(n)>=0&&r!==this._joinRef())return;let h=this._onMessage(n,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===n}).map(e=>e.callback(h,r)):null===(i=this.bindings[n])||void 0===i||i.filter(e=>{var r,s,i,o,a,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(s=t.ids)||void 0===s?void 0:s.includes(n))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(i=t.data)||void 0===i?void 0:i.type.toLocaleLowerCase()))}{let r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof h&&"ids"in h){let e=h.data,{schema:t,table:r,commit_timestamp:s,type:i,errors:n}=e;h=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:s,eventType:i,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===g.closed}_isJoined(){return this.state===g.joined}_isJoining(){return this.state===g.joining}_isLeaving(){return this.state===g.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let s=e.toLocaleLowerCase(),i={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&RealtimeChannel.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(y.close,{},e)}_onError(e){this._on(y.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=g.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=convertChangeData(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=convertChangeData(e.columns,e.old_record)),t}};let RealtimeClient_noop=()=>{},I=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;let RealtimeClient=class RealtimeClient{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=RealtimeClient_noop,this.ref=0,this.logger=RealtimeClient_noop,this.conn=null,this.sendBuffer=[],this.serializer=new Serializer,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${v.websocket}`,this.httpEndpoint=httpEndpointURL(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null===(s=null==t?void 0:t.params)||void 0===s?void 0:s.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Timer(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=R),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case p.connecting:return m.Connecting;case p.open:return m.Open;case p.closing:return m.Closing;default:return m.Closed}}isConnected(){return this.connectionState()===m.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,s=this.getChannels().find(e=>e.topic===r);if(s)return s;{let r=new RealtimeChannel(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:s,ref:i}=e,callback=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${i})`,s),this.isConnected()?callback():this.sendBuffer.push(callback)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:"realtime-js/2.11.15"}),e.joinedOnce&&e._isJoined()&&e._push(y.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:s,ref:i}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,s),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(y.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",s=new URLSearchParams(t);return`${e}${r}${s}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([I],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}};let StorageError=class StorageError extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}};function isStorageError(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}let StorageApiError=class StorageApiError extends StorageError{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}};let StorageUnknownError=class StorageUnknownError extends StorageError{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}};var helpers_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let helpers_resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},resolveResponse=()=>helpers_awaiter(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,9494))).Response:Response}),recursiveToCamel=e=>{if(Array.isArray(e))return e.map(e=>recursiveToCamel(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{let s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=recursiveToCamel(r)}),t};var fetch_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let _getErrorMessage=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),handleError=(e,t,r)=>fetch_awaiter(void 0,void 0,void 0,function*(){let s=yield resolveResponse();e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new StorageApiError(_getErrorMessage(r),e.status||500))}).catch(e=>{t(new StorageUnknownError(_getErrorMessage(e),e))}):t(new StorageUnknownError(_getErrorMessage(e),e))}),_getRequestParams=(e,t,r,s)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),s&&(i.body=JSON.stringify(s)),Object.assign(Object.assign({},i),r))};function _handleRequest(e,t,r,s,i,n){return fetch_awaiter(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,_getRequestParams(t,s,i,n)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>handleError(e,a,s))})})}function get(e,t,r,s){return fetch_awaiter(this,void 0,void 0,function*(){return _handleRequest(e,"GET",t,r,s)})}function post(e,t,r,s,i){return fetch_awaiter(this,void 0,void 0,function*(){return _handleRequest(e,"POST",t,s,i,r)})}function put(e,t,r,s,i){return fetch_awaiter(this,void 0,void 0,function*(){return _handleRequest(e,"PUT",t,s,i,r)})}function head(e,t,r,s){return fetch_awaiter(this,void 0,void 0,function*(){return _handleRequest(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}function remove(e,t,r,s,i){return fetch_awaiter(this,void 0,void 0,function*(){return _handleRequest(e,"DELETE",t,s,i,r)})}var x=r(263).Buffer,StorageFileApi_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let $={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},U={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};let StorageFileApi=class StorageFileApi{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=helpers_resolveFetch(s)}uploadOrUpdate(e,t,r,s){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let i;let n=Object.assign(Object.assign({},U),s),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),a=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((i=new FormData).append("cacheControl",n.cacheControl),a&&i.append("metadata",this.encodeMetadata(a)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((i=r).append("cacheControl",n.cacheControl),a&&i.append("metadata",this.encodeMetadata(a))):(i=r,o["cache-control"]=`max-age=${n.cacheControl}`,o["content-type"]=n.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),h=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:i,headers:o},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),c=yield h.json();if(h.ok)return{data:{path:l,id:c.Id,fullPath:c.Key},error:null};return{data:null,error:c}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}upload(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return StorageFileApi_awaiter(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),n=this._getFinalPath(i),o=new URL(this.url+`/object/upload/sign/${n}`);o.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:U.upsert},s),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:n}),l=yield a.json();if(a.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");let i=yield post(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),n=new URL(this.url+i.url),o=n.searchParams.get("token");if(!o)throw new StorageError("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:o},error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}update(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let s=yield post(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers});return{data:s,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}copy(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let s=yield post(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers});return{data:{path:s.Key},error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),i=yield post(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"",o=encodeURI(`${this.url}${i.signedURL}${n}`);return{data:i={signedUrl:o},error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let s=yield post(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}download(e,t){return StorageFileApi_awaiter(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=s?`?${s}`:"";try{let t=this._getFinalPath(e),s=yield get(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0}),n=yield s.blob();return{data:n,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}info(e){return StorageFileApi_awaiter(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield get(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:recursiveToCamel(e),error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}exists(e){return StorageFileApi_awaiter(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield head(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(isStorageError(e)&&e instanceof StorageUnknownError){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),s=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&s.push(i);let n=void 0!==(null==t?void 0:t.transform),o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let a=s.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${a}`)}}}remove(e){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let t=yield remove(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers});return{data:t,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}list(e,t,r){return StorageFileApi_awaiter(this,void 0,void 0,function*(){try{let s=Object.assign(Object.assign(Object.assign({},$),t),{prefix:e||""}),i=yield post(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r);return{data:i,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==x?x.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}};let L={"X-Client-Info":"storage-js/2.7.1"};var StorageBucketApi_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let StorageBucketApi=class StorageBucketApi{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},L),t),this.fetch=helpers_resolveFetch(r)}listBuckets(){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let e=yield get(this.fetch,`${this.url}/bucket`,{headers:this.headers});return{data:e,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}getBucket(e){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let t=yield get(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers});return{data:t,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let r=yield post(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let r=yield put(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:r,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}emptyBucket(e){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let t=yield post(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers});return{data:t,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}deleteBucket(e){return StorageBucketApi_awaiter(this,void 0,void 0,function*(){try{let t=yield remove(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers});return{data:t,error:null}}catch(e){if(isStorageError(e))return{data:null,error:e};throw e}})}};let StorageClient=class StorageClient extends StorageBucketApi{constructor(e,t={},r){super(e,t,r)}from(e){return new StorageFileApi(this.url,this.headers,e,this.fetch)}};let D="";D="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let N={"X-Client-Info":`supabase-js-${D}/2.51.0`},F={headers:N},M={schema:"public"},W={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},K={};var z=r(9494),lib_fetch_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let fetch_resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?z.default:fetch),(...e)=>t(...e)},resolveHeadersConstructor=()=>"undefined"==typeof Headers?z.Headers:Headers,fetchWithAuth=(e,t,r)=>{let s=fetch_resolveFetch(r),i=resolveHeadersConstructor();return(r,n)=>lib_fetch_awaiter(void 0,void 0,void 0,function*(){var o;let a=null!==(o=yield t())&&void 0!==o?o:e,l=new i(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),s(r,Object.assign(Object.assign({},n),{headers:l}))})};var lib_helpers_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};function ensureTrailingSlash(e){return e.endsWith("/")?e:e+"/"}function applySettingDefaults(e,t){var r,s;let{db:i,auth:n,realtime:o,global:a}=e,{db:l,auth:u,realtime:h,global:c}=t,d={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),n),realtime:Object.assign(Object.assign({},h),o),global:Object.assign(Object.assign(Object.assign({},c),a),{headers:Object.assign(Object.assign({},null!==(r=null==c?void 0:c.headers)&&void 0!==r?r:{}),null!==(s=null==a?void 0:a.headers)&&void 0!==s?s:{})}),accessToken:()=>lib_helpers_awaiter(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}let G="2.71.0",J={"X-Client-Info":`gotrue-js/${G}`},H="X-Supabase-Api-Version",V={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Y=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;let AuthError=class AuthError extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}};function isAuthError(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}let AuthApiError=class AuthApiError extends AuthError{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}};function isAuthApiError(e){return isAuthError(e)&&"AuthApiError"===e.name}let AuthUnknownError=class AuthUnknownError extends AuthError{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}};let CustomAuthError=class CustomAuthError extends AuthError{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}};let AuthSessionMissingError=class AuthSessionMissingError extends CustomAuthError{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}};function isAuthSessionMissingError(e){return isAuthError(e)&&"AuthSessionMissingError"===e.name}let AuthInvalidTokenResponseError=class AuthInvalidTokenResponseError extends CustomAuthError{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}};let AuthInvalidCredentialsError=class AuthInvalidCredentialsError extends CustomAuthError{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}};let AuthImplicitGrantRedirectError=class AuthImplicitGrantRedirectError extends CustomAuthError{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}};function isAuthImplicitGrantRedirectError(e){return isAuthError(e)&&"AuthImplicitGrantRedirectError"===e.name}let AuthPKCEGrantCodeExchangeError=class AuthPKCEGrantCodeExchangeError extends CustomAuthError{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}};let AuthRetryableFetchError=class AuthRetryableFetchError extends CustomAuthError{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}};function isAuthRetryableFetchError(e){return isAuthError(e)&&"AuthRetryableFetchError"===e.name}let AuthWeakPasswordError=class AuthWeakPasswordError extends CustomAuthError{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}};let AuthInvalidJwtError=class AuthInvalidJwtError extends CustomAuthError{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}};let Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),X=" 	\n\r=".split(""),Z=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<X.length;t+=1)e[X[t].charCodeAt(0)]=-2;for(let t=0;t<Q.length;t+=1)e[Q[t].charCodeAt(0)]=t;return e})();function byteToBase64URL(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){let e=t.queue>>t.queuedBits-6&63;r(Q[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){let e=t.queue>>t.queuedBits-6&63;r(Q[e]),t.queuedBits-=6}}function byteFromBase64URL(e,t,r){let s=Z[e];if(s>-1)for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===s)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function stringFromBase64URL(e){let t=[],utf8Emit=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},byteEmit=e=>{stringFromUTF8(e,r,utf8Emit)};for(let t=0;t<e.length;t+=1)byteFromBase64URL(e.charCodeAt(t),s,byteEmit);return t.join("")}function codepointToUTF8(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function stringToUTF8(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){let t=(s-55296)*1024&65535,i=e.charCodeAt(r+1)-56320&65535;s=(i|t)+65536,r+=1}codepointToUTF8(s,t)}}function stringFromUTF8(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}function base64UrlToUint8Array(e){let t=[],r={queue:0,queuedBits:0},onByte=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)byteFromBase64URL(e.charCodeAt(t),r,onByte);return new Uint8Array(t)}function stringToUint8Array(e){let t=[];return stringToUTF8(e,e=>t.push(e)),new Uint8Array(t)}function bytesToBase64URL(e){let t=[],r={queue:0,queuedBits:0},onChar=e=>{t.push(e)};return e.forEach(e=>byteToBase64URL(e,r,onChar)),byteToBase64URL(null,r,onChar),t.join("")}function expiresAt(e){let t=Math.round(Date.now()/1e3);return t+e}function helpers_uuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}let helpers_isBrowser=()=>"undefined"!=typeof window&&"undefined"!=typeof document,ee={tested:!1,writable:!1},supportsLocalStorage=()=>{if(!helpers_isBrowser())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(ee.tested)return ee.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),ee.tested=!0,ee.writable=!0}catch(e){ee.tested=!0,ee.writable=!1}return ee.writable};function parseParametersFromURL(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{let e=new URLSearchParams(r.hash.substring(1));e.forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}let lib_helpers_resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},looksLikeFetchResponse=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,setItemAsync=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},getItemAsync=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},removeItemAsync=async(e,t)=>{await e.removeItem(t)};let Deferred=class Deferred{constructor(){this.promise=new Deferred.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}};function decodeJWT(e){let t=e.split(".");if(3!==t.length)throw new AuthInvalidJwtError("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!Y.test(t[e]))throw new AuthInvalidJwtError("JWT not in base64url format");let r={header:JSON.parse(stringFromBase64URL(t[0])),payload:JSON.parse(stringFromBase64URL(t[1])),signature:base64UrlToUint8Array(t[2]),raw:{header:t[0],payload:t[1]}};return r}async function sleep(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function retryable(e,t){let r=new Promise((r,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{let s=await e(i);if(!t(i,null,s)){r(s);return}}catch(e){if(!t(i,e)){s(e);return}}})()});return r}function dec2hex(e){return("0"+e.toString(16)).substr(-2)}function generatePKCEVerifier(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,dec2hex).join("")}async function sha256(e){let t=new TextEncoder,r=t.encode(e),s=await crypto.subtle.digest("SHA-256",r),i=new Uint8Array(s);return Array.from(i).map(e=>String.fromCharCode(e)).join("")}async function generatePKCEChallenge(e){let t="undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder;if(!t)return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;let r=await sha256(e);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function getCodeChallengeAndMethod(e,t,r=!1){let s=generatePKCEVerifier(),i=s;r&&(i+="/PASSWORD_RECOVERY"),await setItemAsync(e,`${t}-code-verifier`,i);let n=await generatePKCEChallenge(s),o=s===n?"plain":"s256";return[n,o]}Deferred.promiseConstructor=Promise;let et=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function parseResponseAPIVersion(e){let t=e.headers.get(H);if(!t||!t.match(et))return null;try{let e=new Date(`${t}T00:00:00.0Z`);return e}catch(e){return null}}function validateExp(e){if(!e)throw Error("Missing exp claim");let t=Math.floor(Date.now()/1e3);if(e<=t)throw Error("JWT has expired")}function getAlgorithm(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}let er=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function validateUUID(e){if(!er.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function userNotAvailableProxy(){return new Proxy({},{get:(e,t)=>{if("__isUserNotAvailableProxy"===t)return!0;if("symbol"==typeof t){let e=t.toString();if("Symbol(Symbol.toPrimitive)"===e||"Symbol(Symbol.toStringTag)"===e||"Symbol(util.inspect.custom)"===e)return}throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${t}" property of the session object is not supported. Please use getUser() instead.`)},set:(e,t)=>{throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(e,t)=>{throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}var __rest=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(e);i<s.length;i++)0>t.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]]);return r};let fetch_getErrorMessage=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),es=[502,503,504];async function fetch_handleError(e){var t;let r,s;if(!looksLikeFetchResponse(e))throw new AuthRetryableFetchError(fetch_getErrorMessage(e),0);if(es.includes(e.status))throw new AuthRetryableFetchError(fetch_getErrorMessage(e),e.status);try{r=await e.json()}catch(e){throw new AuthUnknownError(fetch_getErrorMessage(e),e)}let i=parseResponseAPIVersion(e);if(i&&i.getTime()>=V["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?s=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(s=r.error_code),s){if("weak_password"===s)throw new AuthWeakPasswordError(fetch_getErrorMessage(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===s)throw new AuthSessionMissingError}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new AuthWeakPasswordError(fetch_getErrorMessage(r),e.status,r.weak_password.reasons);throw new AuthApiError(fetch_getErrorMessage(r),e.status||500,s)}let fetch_getRequestParams=(e,t,r,s)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(s),Object.assign(Object.assign({},i),r))};async function _request(e,t,r,s){var i;let n=Object.assign({},null==s?void 0:s.headers);n[H]||(n[H]=V["2024-01-01"].name),(null==s?void 0:s.jwt)&&(n.Authorization=`Bearer ${s.jwt}`);let o=null!==(i=null==s?void 0:s.query)&&void 0!==i?i:{};(null==s?void 0:s.redirectTo)&&(o.redirect_to=s.redirectTo);let a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await fetch_handleRequest(e,t,r+a,{headers:n,noResolveJson:null==s?void 0:s.noResolveJson},{},null==s?void 0:s.body);return(null==s?void 0:s.xform)?null==s?void 0:s.xform(l):{data:Object.assign({},l),error:null}}async function fetch_handleRequest(e,t,r,s,i,n){let o;let a=fetch_getRequestParams(t,s,i,n);try{o=await e(r,Object.assign({},a))}catch(e){throw console.error(e),new AuthRetryableFetchError(fetch_getErrorMessage(e),0)}if(o.ok||await fetch_handleError(o),null==s?void 0:s.noResolveJson)return o;try{return await o.json()}catch(e){await fetch_handleError(e)}}function _sessionResponse(e){var t;let r=null;hasSession(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=expiresAt(e.expires_in)));let s=null!==(t=e.user)&&void 0!==t?t:e;return{data:{session:r,user:s},error:null}}function _sessionResponsePassword(e){let t=_sessionResponse(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function _userResponse(e){var t;let r=null!==(t=e.user)&&void 0!==t?t:e;return{data:{user:r},error:null}}function _ssoResponse(e){return{data:e,error:null}}function _generateLinkResponse(e){let{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n}=e,o=__rest(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a=Object.assign({},o);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:i,verification_type:n},user:a},error:null}}function _noResolveJsonResponse(e){return e}function hasSession(e){return e.access_token&&e.refresh_token&&e.expires_in}let ei=["global","local","others"];var GoTrueAdminApi_rest=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(e);i<s.length;i++)0>t.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]]);return r};let GoTrueAdminApi_GoTrueAdminApi=class GoTrueAdminApi_GoTrueAdminApi{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=lib_helpers_resolveFetch(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=ei[0]){if(0>ei.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${ei.join(", ")}`);try{return await _request(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await _request(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:_userResponse})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=GoTrueAdminApi_rest(e,["options"]),s=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),await _request(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:_generateLinkResponse,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(isAuthError(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await _request(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:_userResponse})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,s,i,n,o,a;try{let l={nextPage:null,lastPage:0,total:0},u=await _request(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(i=null===(s=null==e?void 0:e.perPage)||void 0===s?void 0:s.toString())&&void 0!==i?i:""},xform:_noResolveJsonResponse});if(u.error)throw u.error;let h=await u.json(),c=null!==(n=u.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(a=null===(o=u.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(c)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(e){if(isAuthError(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){validateUUID(e);try{return await _request(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:_userResponse})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){validateUUID(e);try{return await _request(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:_userResponse})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){validateUUID(e);try{return await _request(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:_userResponse})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){validateUUID(e.userId);try{let{data:t,error:r}=await _request(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async _deleteFactor(e){validateUUID(e.userId),validateUUID(e.id);try{let t=await _request(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers});return{data:t,error:null}}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}};function memoryLocalStorageAdapter(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}function polyfillGlobalThis(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}let en={debug:!!(globalThis&&supportsLocalStorage()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};let LockAcquireTimeoutError=class LockAcquireTimeoutError extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}};let NavigatorLockAcquireTimeoutError=class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError{};async function navigatorLock(e,t,r){en.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),en.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async s=>{if(s){en.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{en.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(0===t)throw en.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(en.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}polyfillGlobalThis();let eo={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:J,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function lockNoOp(e,t,r){return await r()}let ea={};let GoTrueClient=class GoTrueClient{constructor(e){var t,r;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=GoTrueClient.nextInstanceID,GoTrueClient.nextInstanceID+=1,this.instanceID>0&&helpers_isBrowser()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let s=Object.assign(Object.assign({},eo),e);if(this.logDebugMessages=!!s.debug,"function"==typeof s.debug&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new GoTrueAdminApi_GoTrueAdminApi({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=lib_helpers_resolveFetch(s.fetch),this.lock=s.lock||lockNoOp,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:helpers_isBrowser()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=navigatorLock:this.lock=lockNoOp,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(s.storage?this.storage=s.storage:supportsLocalStorage()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=memoryLocalStorageAdapter(this.memoryStorage)),s.userStorage&&(this.userStorage=s.userStorage)):(this.memoryStorage={},this.storage=memoryLocalStorageAdapter(this.memoryStorage)),helpers_isBrowser()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}get jwks(){var e,t;return null!==(t=null===(e=ea[this.storageKey])||void 0===e?void 0:e.jwks)&&void 0!==t?t:{keys:[]}}set jwks(e){ea[this.storageKey]=Object.assign(Object.assign({},ea[this.storageKey]),{jwks:e})}get jwks_cached_at(){var e,t;return null!==(t=null===(e=ea[this.storageKey])||void 0===e?void 0:e.cachedAt)&&void 0!==t?t:Number.MIN_SAFE_INTEGER}set jwks_cached_at(e){ea[this.storageKey]=Object.assign(Object.assign({},ea[this.storageKey]),{cachedAt:e})}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${G}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=parseParametersFromURL(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),helpers_isBrowser()&&this.detectSessionInUrl&&"none"!==r){let{data:s,error:i}=await this._getSessionFromURL(t,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),isAuthImplicitGrantRedirectError(i)){let t=null===(e=i.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:n,redirectType:o}=s;return this._debug("#_initialize()","detected session in URL",n,"redirect type",o),await this._saveSession(n),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(isAuthError(e))return{error:e};return{error:new AuthUnknownError("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,s;try{let i=await _request(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken}},xform:_sessionResponse}),{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};let a=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,s;try{let i;if("email"in e){let{email:r,password:s,options:n}=e,o=null,a=null;"pkce"===this.flowType&&([o,a]=await getCodeChallengeAndMethod(this.storage,this.storageKey)),i=await _request(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:s,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:o,code_challenge_method:a},xform:_sessionResponse})}else if("phone"in e){let{phone:t,password:n,options:o}=e;i=await _request(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:_sessionResponse})}else throw new AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:n,error:o}=i;if(o||!n)return{data:{user:null,session:null},error:o};let a=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:s,options:i}=e;t=await _request(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:_sessionResponsePassword})}else if("phone"in e){let{phone:r,password:s,options:i}=e;t=await _request(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:_sessionResponsePassword})}else throw new AuthInvalidCredentialsError("You must provide either an email or phone number and a password");let{data:r,error:s}=t;if(s)return{data:{user:null,session:null},error:s};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new AuthInvalidTokenResponseError};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,s,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(i=e.options)||void 0===i?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,s,i,n,o,a,l,u,h,c,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let c;let{chain:d,wallet:g,statement:y,options:v}=e;if(helpers_isBrowser()){if("object"==typeof g)c=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))c=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}}else{if("object"!=typeof g||!(null==v?void 0:v.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");c=g}let m=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in c&&c.signIn){let e;let t=await c.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:m.host,uri:m.href}),y?{statement:y}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in c)||"function"!=typeof c.signMessage||!("publicKey"in c)||"object"!=typeof c||!c.publicKey||!("toBase58"in c.publicKey)||"function"!=typeof c.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${m.host} wants you to sign in with your Solana account:`,c.publicKey.toBase58(),...y?["",y,""]:[""],"Version: 1",`URI: ${m.href}`,`Issued At: ${null!==(s=null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==s?s:new Date().toISOString()}`,...(null===(i=null==v?void 0:v.signInWithSolana)||void 0===i?void 0:i.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(h=null===(u=null==v?void 0:v.signInWithSolana)||void 0===u?void 0:u.resources)||void 0===h?void 0:h.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await c.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await _request(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:bytesToBase64URL(p)},(null===(c=e.options)||void 0===c?void 0:c.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:_sessionResponse});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new AuthInvalidTokenResponseError};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await getItemAsync(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await _request(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:_sessionResponse});if(await removeItemAsync(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new AuthInvalidTokenResponseError};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=s?s:null}),error:i}}catch(e){if(isAuthError(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:s,access_token:i,nonce:n}=e,o=await _request(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:_sessionResponse}),{data:a,error:l}=o;if(l)return{data:{user:null,session:null},error:l};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new AuthInvalidTokenResponseError};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:l}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,s,i,n;try{if("email"in e){let{email:s,options:i}=e,n=null,o=null;"pkce"===this.flowType&&([n,o]=await getCodeChallengeAndMethod(this.storage,this.storageKey));let{error:a}=await _request(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},create_user:null===(r=null==i?void 0:i.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:n,code_challenge_method:o},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:t,options:r}=e,{data:o,error:a}=await _request(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(i=null==r?void 0:r.shouldCreateUser)||void 0===i||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new AuthInvalidCredentialsError("You must provide either an email or phone number.")}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let s,i;"options"in e&&(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo,i=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:n,error:o}=await _request(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:_sessionResponse});if(o)throw o;if(!n)throw Error("An error occurred on token verification.");let a=n.session,l=n.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,s;try{let i=null,n=null;return"pkce"===this.flowType&&([i,n]=await getCodeChallengeAndMethod(this.storage,this.storageKey)),await _request(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==e?void 0:e.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:n}),headers:this.headers,xform:_ssoResponse})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new AuthSessionMissingError;let{error:s}=await _request(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:s}})}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:s,options:i}=e,{error:n}=await _request(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:s,options:i}=e,{data:n,error:o}=await _request(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new AuthInvalidCredentialsError("You must provide either an email or phone number and a type")}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){await this.initializePromise;let e=await this._acquireLock(-1,async()=>this._useSession(async e=>e));return e}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await getItemAsync(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.userStorage){let t=await getItemAsync(this.userStorage,this.storageKey+"-user");(null==t?void 0:t.user)?e.user=t.user:e.user=userNotAvailableProxy()}if(this.storage.isServer&&e.user){let t=this.suppressGetSessionWarning,r=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))});e=r}return{data:{session:e},error:null}}let{session:s,error:i}=await this._callRefreshToken(e.refresh_token);if(i)return{data:{session:null},error:i};return{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;let t=await this._acquireLock(-1,async()=>await this._getUser());return t}async _getUser(e){try{if(e)return await _request(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:_userResponse});return await this._useSession(async e=>{var t,r,s;let{data:i,error:n}=e;if(n)throw n;return(null===(t=i.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await _request(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=i.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:_userResponse}):{data:{user:null},error:new AuthSessionMissingError}})}catch(e){if(isAuthError(e))return isAuthSessionMissingError(e)&&(await this._removeSession(),await removeItemAsync(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:s,error:i}=r;if(i)throw i;if(!s.session)throw new AuthSessionMissingError;let n=s.session,o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await getCodeChallengeAndMethod(this.storage,this.storageKey));let{data:l,error:u}=await _request(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:n.access_token,xform:_userResponse});if(u)throw u;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(isAuthError(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new AuthSessionMissingError;let t=Date.now()/1e3,r=t,s=!0,i=null,{payload:n}=decodeJWT(e.access_token);if(n.exp&&(s=(r=n.exp)<=t),s){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:s,error:n}=await this._getUser(e.access_token);if(n)throw n;i={access_token:e.access_token,refresh_token:e.refresh_token,user:s.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(isAuthError(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:s,error:i}=t;if(i)throw i;e=null!==(r=s.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new AuthSessionMissingError;let{session:s,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(isAuthError(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!helpers_isBrowser())throw new AuthImplicitGrantRedirectError("No browser detected.");if(e.error||e.error_description||e.error_code)throw new AuthImplicitGrantRedirectError(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new AuthPKCEGrantCodeExchangeError("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new AuthImplicitGrantRedirectError("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new AuthPKCEGrantCodeExchangeError("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:s,access_token:i,refresh_token:n,expires_in:o,expires_at:a,token_type:l}=e;if(!i||!o||!n||!l)throw new AuthImplicitGrantRedirectError("No session defined in URL");let u=Math.round(Date.now()/1e3),h=parseInt(o),c=u+h;a&&(c=parseInt(a));let d=c-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${h}s`);let f=c-h;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,c,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,c,u);let{data:p,error:g}=await this._getUser(i);if(g)throw g;let y={provider_token:r,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:c,refresh_token:n,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:y,redirectType:e.type},error:null}}catch(e){if(isAuthError(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await getItemAsync(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:s,error:i}=t;if(i)return{error:i};let n=null===(r=s.session)||void 0===r?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(isAuthApiError(t)&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await removeItemAsync(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t=helpers_uuid(),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,s;try{let{data:{session:s},error:i}=t;if(i)throw i;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",e,"session",s)}catch(t){await (null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=await getCodeChallengeAndMethod(this.storage,this.storageKey,!0));try{return await _request(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:s}=await this._useSession(async t=>{var r,s,i,n,o;let{data:a,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:!0});return await _request(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(o=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==o?o:void 0})});if(s)throw s;return!helpers_isBrowser()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(isAuthError(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,s;let{data:i,error:n}=t;if(n)throw n;return await _request(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(r=i.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0})})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{let r=Date.now();return await retryable(async r=>(r>0&&await sleep(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await _request(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:_sessionResponse})),(e,t)=>t&&isAuthRetryableFetchError(t)&&Date.now()+200*Math.pow(2,e)-r<3e4)}catch(e){if(this._debug(t,"error",e),isAuthError(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),helpers_isBrowser()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e,t;let r="#_recoverAndRefresh()";this._debug(r,"begin");try{let s=await getItemAsync(this.storage,this.storageKey);if(s&&this.userStorage){let t=await getItemAsync(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!t&&(t={user:s.user},await setItemAsync(this.userStorage,this.storageKey+"-user",t)),s.user=null!==(e=null==t?void 0:t.user)&&void 0!==e?e:userNotAvailableProxy()}else if(s&&!s.user&&!s.user){let e=await getItemAsync(this.storage,this.storageKey+"-user");e&&(null==e?void 0:e.user)?(s.user=e.user,await removeItemAsync(this.storage,this.storageKey+"-user"),await setItemAsync(this.storage,this.storageKey,s)):s.user=userNotAvailableProxy()}if(this._debug(r,"session from storage",s),!this._isValidSession(s)){this._debug(r,"session is not valid"),null!==s&&await this._removeSession();return}let i=(null!==(t=s.expires_at)&&void 0!==t?t:1/0)*1e3-Date.now()<9e4;if(this._debug(r,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&s.refresh_token){let{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),isAuthRetryableFetchError(e)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else if(s.user&&!0===s.user.__isUserNotAvailableProxy)try{let{data:e,error:t}=await this._getUser(s.access_token);!t&&(null==e?void 0:e.user)?(s.user=e.user,await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)):this._debug(r,"could not get user data, skipping SIGNED_IN notification")}catch(e){console.error("Error getting user data:",e),this._debug(r,"error getting user data, skipping SIGNED_IN notification",e)}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(e){this._debug(r,"error",e),console.error(e);return}finally{this._debug(r,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new AuthSessionMissingError;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Deferred;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new AuthSessionMissingError;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let s={session:t.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(e){if(this._debug(s,"error",e),isAuthError(e)){let r={session:null,error:e};return isAuthRetryableFetchError(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(e,t,r=!0){let s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let s=[],i=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){s.push(e)}});if(await Promise.all(i),s.length>0){for(let e=0;e<s.length;e+=1)console.error(s[e]);throw s[0]}}finally{this._debug(s,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0;let t=Object.assign({},e),r=t.user&&!0===t.user.__isUserNotAvailableProxy;if(this.userStorage){!r&&t.user&&await setItemAsync(this.userStorage,this.storageKey+"-user",{user:t.user});let e=Object.assign({},t);delete e.user;let s=structuredClone(e);await setItemAsync(this.storage,this.storageKey,s)}else{let e=structuredClone(t);await setItemAsync(this.storage,this.storageKey,e)}}async _removeSession(){this._debug("#_removeSession()"),await removeItemAsync(this.storage,this.storageKey),await removeItemAsync(this.storage,this.storageKey+"-code-verifier"),await removeItemAsync(this.storage,this.storageKey+"-user"),this.userStorage&&await removeItemAsync(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&helpers_isBrowser()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let s=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof LockAcquireTimeoutError)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!helpers_isBrowser()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let s=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await getCodeChallengeAndMethod(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});s.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);s.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${s.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;return i?{data:null,error:i}:await _request(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,s;let{data:i,error:n}=t;if(n)return{data:null,error:n};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:l}=await _request(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(s=null==a?void 0:a.totp)||void 0===s?void 0:s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;if(i)return{data:null,error:i};let{data:n,error:o}=await _request(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:s,error:i}=t;return i?{data:null,error:i}:await _request(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token})})}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:s},error:i}=e;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=decodeJWT(s.access_token),o=null;n.aal&&(o=n.aal);let a=o,l=null!==(r=null===(t=s.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[];l.length>0&&(a="aal2");let u=n.amr||[];return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:u},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;let s=Date.now();if((r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>s)return r;let{data:i,error:n}=await _request(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;return i.keys&&0!==i.keys.length&&(this.jwks=i,this.jwks_cached_at=s,r=i.keys.find(t=>t.kid===e))?r:null}async getClaims(e,t={}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:s,payload:i,signature:n,raw:{header:o,payload:a}}=decodeJWT(r);(null==t?void 0:t.allowExpired)||validateExp(i.exp);let l=!s.alg||s.alg.startsWith("HS")||!s.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:await this.fetchJwk(s.kid,(null==t?void 0:t.keys)?{keys:t.keys}:null==t?void 0:t.jwks);if(!l){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:i,header:s,signature:n},error:null}}let u=getAlgorithm(s.alg),h=await crypto.subtle.importKey("jwk",l,u,!0,["verify"]),c=await crypto.subtle.verify(u,h,n,stringToUint8Array(`${o}.${a}`));if(!c)throw new AuthInvalidJwtError("Invalid JWT signature");return{data:{claims:i,header:s,signature:n},error:null}}catch(e){if(isAuthError(e))return{data:null,error:e};throw e}}};GoTrueClient.nextInstanceID=0;var el=GoTrueClient;let SupabaseAuthClient=class SupabaseAuthClient extends el{constructor(e){super(e)}};var SupabaseClient_awaiter=function(e,t,r,s){function adopt(e){return e instanceof r?e:new r(function(t){t(e)})}return new(r||(r=Promise))(function(r,i){function fulfilled(e){try{step(s.next(e))}catch(e){i(e)}}function rejected(e){try{step(s.throw(e))}catch(e){i(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((s=s.apply(e,t||[])).next())})};let SupabaseClient=class SupabaseClient{constructor(e,t,r){var s,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let o=ensureTrailingSlash(e),a=new URL(o);this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let l=`sb-${a.hostname.split(".")[0]}-auth-token`,u={db:M,realtime:K,auth:Object.assign(Object.assign({},W),{storageKey:l}),global:F},h=applySettingDefaults(null!=r?r:{},u);this.storageKey=null!==(s=h.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(i=h.global.headers)&&void 0!==i?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=h.auth)&&void 0!==n?n:{},this.headers,h.global.fetch),this.fetch=fetchWithAuth(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new T(new URL("rest/v1",a).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new FunctionsClient(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new StorageClient(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return SupabaseClient_awaiter(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:i,flowType:n,lock:o,debug:a},l,u){let h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new SupabaseAuthClient({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:n,lock:o,debug:a,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new RealtimeClient(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}};let createClient=(e,t,r)=>new SupabaseClient(e,t,r);function _isPlaceholder(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function _curry1(e){return function f1(t){return 0==arguments.length||_isPlaceholder(t)?f1:e.apply(this,arguments)}}function _curry2(e){return function f2(t,r){switch(arguments.length){case 0:return f2;case 1:return _isPlaceholder(t)?f2:_curry1(function(r){return e(t,r)});default:return _isPlaceholder(t)&&_isPlaceholder(r)?f2:_isPlaceholder(t)?_curry1(function(t){return e(t,r)}):_isPlaceholder(r)?_curry1(function(r){return e(t,r)}):e(t,r)}}}function _curry3(e){return function f3(t,r,s){switch(arguments.length){case 0:return f3;case 1:return _isPlaceholder(t)?f3:_curry2(function(r,s){return e(t,r,s)});case 2:return _isPlaceholder(t)&&_isPlaceholder(r)?f3:_isPlaceholder(t)?_curry2(function(t,s){return e(t,r,s)}):_isPlaceholder(r)?_curry2(function(r,s){return e(t,r,s)}):_curry1(function(s){return e(t,r,s)});default:return _isPlaceholder(t)&&_isPlaceholder(r)&&_isPlaceholder(s)?f3:_isPlaceholder(t)&&_isPlaceholder(r)?_curry2(function(t,r){return e(t,r,s)}):_isPlaceholder(t)&&_isPlaceholder(s)?_curry2(function(t,s){return e(t,r,s)}):_isPlaceholder(r)&&_isPlaceholder(s)?_curry2(function(r,s){return e(t,r,s)}):_isPlaceholder(t)?_curry1(function(t){return e(t,r,s)}):_isPlaceholder(r)?_curry1(function(r){return e(t,r,s)}):_isPlaceholder(s)?_curry1(function(s){return e(t,r,s)}):e(t,r,s)}}}function _isObject(e){return"[object Object]"===Object.prototype.toString.call(e)}function _has(e,t){return Object.prototype.hasOwnProperty.call(t,e)}var eu=_curry3(function(e,t,r){var s,i={};for(s in r=r||{},t=t||{})_has(s,t)&&(i[s]=_has(s,r)?e(s,t[s],r[s]):t[s]);for(s in r)_has(s,r)&&!_has(s,i)&&(i[s]=r[s]);return i}),eh=_curry3(function mergeDeepWithKey(e,t,r){return eu(function(t,r,s){return _isObject(r)&&_isObject(s)?mergeDeepWithKey(e,r,s):e(t,r,s)},t,r)}),ec=_curry2(function(e,t){return eh(function(e,t,r){return r},e,t)}),ed=r(4482);function dist_isBrowser(){return"undefined"!=typeof window&&void 0!==window.document}var ef={path:"/",sameSite:"lax",httpOnly:!1,maxAge:31536e6};function createChunks(e,t,r){let s=r??3180,i=encodeURIComponent(t);if(i.length<=s)return[{name:e,value:t}];let n=[];for(;i.length>0;){let e=i.slice(0,s),t=e.lastIndexOf("%");t>s-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(r),i=i.slice(e.length)}return n.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function combineChunks(e,t){let r=await t(e);if(r)return r;let s=[];for(let r=0;;r++){let i=`${e}.${r}`,n=await t(i);if(!n)break;s.push(n)}if(s.length>0)return s.join("")}async function deleteChunks(e,t,r){let s=await t(e);if(s){await r(e);return}for(let s=0;;s++){let i=`${e}.${s}`,n=await t(i);if(!n)break;await r(i)}}function createBrowserClient(e,t,r){let s,i;if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let n={},o=!0;r&&({cookies:n,isSingleton:o=!0,cookieOptions:s,...i}=r);let a={global:{headers:{"X-Client-Info":"supabase-ssr/0.1.0"}},auth:{flowType:"pkce",autoRefreshToken:dist_isBrowser(),detectSessionInUrl:dist_isBrowser(),persistSession:!0,storage:{isServer:!1,getItem:async e=>{let t=await combineChunks(e,async e=>{if("function"==typeof n.get)return await n.get(e);if(dist_isBrowser()){let t=(0,ed.Q)(document.cookie);return t[e]}});return t},setItem:async(e,t)=>{let r=await createChunks(e,t);await Promise.all(r.map(async e=>{"function"==typeof n.set?await n.set(e.name,e.value,{...ef,...s,maxAge:ef.maxAge}):dist_isBrowser()&&(document.cookie=(0,ed.q)(e.name,e.value,{...ef,...s,maxAge:ef.maxAge}))}))},removeItem:async e=>{if("function"==typeof n.remove&&"function"!=typeof n.get){console.log("Removing chunked cookie without a `get` method is not supported.\n\n	When you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\n\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client");return}await deleteChunks(e,async e=>{if("function"==typeof n.get)return await n.get(e);if(dist_isBrowser()){let t=(0,ed.Q)(document.cookie);return t[e]}},async e=>{"function"==typeof n.remove?await n.remove(e,{...ef,...s,maxAge:0}):dist_isBrowser()&&(document.cookie=(0,ed.q)(e,"",{...ef,...s,maxAge:0}))})}}}},l=ec(a,i);if(o){let r=dist_isBrowser();if(r&&S)return S;let s=createClient(e,t,l);return r&&(S=s),s}return createClient(e,t,l)}}}]);