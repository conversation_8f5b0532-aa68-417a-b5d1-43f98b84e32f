(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[966],{886:function(e,a,r){Promise.resolve().then(r.bind(r,2578))},2578:function(e,a,r){"use strict";r.r(a),r.d(a,{default:function(){return SignupPage}});var t=r(7437),s=r(2265),n=r(4033),l=r(1865),i=r(8110),o=r(1396),c=r.n(o),d=r(6691),u=r.n(d),m=r(7341),g=r(8007);function Toast(e){let{message:a,type:r,show:n,onClose:l,duration:i=5e3}=e,[o,c]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{if(n){c(!0);let e=setTimeout(()=>{c(!1),setTimeout(l,300)},i);return()=>clearTimeout(e)}},[n,i,l]),!n&&!o)return null;let d={success:"text-green-400",error:"text-red-400",info:"text-primary-400"}[r],u={success:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),error:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),info:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}[r];return(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 transition-all duration-300 ".concat(o?"translate-y-0 opacity-100":"-translate-y-2 opacity-0"),children:(0,t.jsx)("div",{className:"max-w-sm rounded-lg border p-4 shadow-lg ".concat({success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",info:"bg-primary-50 border-primary-200 text-primary-800"}[r]),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 ".concat(d),children:u}),(0,t.jsx)("div",{className:"ml-3 w-0 flex-1",children:(0,t.jsx)("p",{className:"text-sm font-medium",children:a})}),(0,t.jsx)("div",{className:"ml-4 flex-shrink-0 flex",children:(0,t.jsxs)("button",{type:"button",className:"inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ".concat(d),onClick:()=>{c(!1),setTimeout(l,300)},children:[(0,t.jsx)("span",{className:"sr-only",children:"Close"}),(0,t.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})})})}var x=r(2160);let h=x.Z_().min(2,"Business name must be at least 2 characters").max(100,"Business name cannot exceed 100 characters").regex(/^[a-zA-Z0-9\s\-'&.]+$/,"Business name contains invalid characters").transform(e=>e.trim()),p=x.Z_().min(3,"Tenant slug must be at least 3 characters").max(50,"Tenant slug cannot exceed 50 characters").regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/,"Slug can only contain lowercase letters, numbers, and hyphens").refine(e=>!e.startsWith("-")&&!e.endsWith("-"),"Slug cannot start or end with hyphen").refine(e=>!e.includes("--"),"Slug cannot contain consecutive hyphens").transform(e=>e.toLowerCase().trim()),y=x.Z_().email("Please enter a valid email address").max(255,"Email cannot exceed 255 characters").transform(e=>e.toLowerCase().trim()),f=x.Z_().min(8,"Password must be at least 8 characters").max(128,"Password cannot exceed 128 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[!@#$%^&*(),.?":{}|<>]/,"Password must contain at least one special character"),b=x.Ry({businessName:h,tenantSlug:p,adminEmail:y,password:f});function SignupPage(){let e=(0,n.useRouter)(),[a,r]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1),[x,h]=(0,s.useState)(!1),[p,y]=(0,s.useState)({show:!1,message:"",type:"info"}),{generateSlug:f,validateSlugAvailability:w,isValidSlugFormat:j,isReservedSlug:v,isValidating:k}=(0,g.M)(),{control:N,handleSubmit:C,watch:S,setValue:z,setError:L,clearErrors:M,formState:{errors:_,isValid:R}}=(0,l.cI)({resolver:(0,i.F)(b),mode:"onChange",defaultValues:{businessName:"",tenantSlug:"",adminEmail:"",password:""}}),Z=S("businessName"),I=S("tenantSlug");(0,s.useEffect)(()=>{if(Z){let e=f(Z);z("tenantSlug",e,{shouldValidate:!0})}},[Z,f,z]),(0,s.useEffect)(()=>{let validateSlug=async()=>{if(I&&j(I)){if(v(I)){L("tenantSlug",{type:"manual",message:"This slug is reserved and cannot be used"});return}try{let e=await w(I);e?M("tenantSlug"):L("tenantSlug",{type:"manual",message:"This slug is already taken"})}catch(e){console.error("Slug validation error:",e)}}},e=setTimeout(validateSlug,500);return()=>clearTimeout(e)},[I,j,v,w,L,M]);let onSubmit=async a=>{r(!0);try{var t;let r=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),s=await r.json();if(!r.ok||!s.success){s.details?Object.entries(s.details).forEach(e=>{let[a,r]=e;L(a,{type:"manual",message:r[0]})}):y({show:!0,message:s.error||"Signup failed. Please try again.",type:"error"});return}(null===(t=s.data)||void 0===t?void 0:t.session)&&await m.OQ.auth.setSession({access_token:s.data.session.access_token,refresh_token:s.data.session.refresh_token}),y({show:!0,message:"Account created successfully! Redirecting to dashboard...",type:"success"}),setTimeout(()=>{e.push("/t/".concat(a.tenantSlug,"/dashboard"))},1500)}catch(e){console.error("Signup error:",e),y({show:!0,message:"An unexpected error occurred. Please try again.",type:"error"})}finally{r(!1)}},handleGoogleSignup=async()=>{h(!0);try{let{data:e,error:a}=await m.OQ.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback?type=signup"),queryParams:{access_type:"offline",prompt:"consent"}}});a&&y({show:!0,message:"Google signup failed. Please try again.",type:"error"})}catch(e){console.error("Google signup error:",e),y({show:!0,message:"Google signup failed. Please try again.",type:"error"})}finally{h(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)(u(),{src:"/logo.svg",alt:"TapDine",width:60,height:60,className:"h-15 w-15"})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create your restaurant account"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Join thousands of restaurants using TapDine"})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("button",{type:"button",onClick:handleGoogleSignup,disabled:x,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,t.jsxs)("svg",{className:"w-5 h-5 mr-3",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),x?"Signing up...":"Continue with Google"]})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400",children:"Or continue with email"})})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:C(onSubmit),children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"businessName",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Business Name *"}),(0,t.jsx)(l.Qr,{name:"businessName",control:N,render:e=>{let{field:a}=e;return(0,t.jsx)("input",{...a,id:"businessName",type:"text",autoComplete:"organization",className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Your Restaurant Name"})}}),_.businessName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.businessName.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"tenantSlug",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Restaurant URL *"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm",children:"tapdine.com/"}),(0,t.jsx)(l.Qr,{name:"tenantSlug",control:N,render:e=>{let{field:a}=e;return(0,t.jsx)("input",{...a,id:"tenantSlug",type:"text",className:"flex-1 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"your-restaurant"})}})]}),k&&(0,t.jsx)("p",{className:"mt-1 text-sm text-primary-600 dark:text-primary-400",children:"Checking availability..."}),_.tenantSlug&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.tenantSlug.message}),!_.tenantSlug&&I&&j(I)&&!v(I)&&(0,t.jsx)("p",{className:"mt-1 text-sm text-green-600 dark:text-green-400",children:"✓ URL is available"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"adminEmail",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Admin Email *"}),(0,t.jsx)(l.Qr,{name:"adminEmail",control:N,render:e=>{let{field:a}=e;return(0,t.jsx)("input",{...a,id:"adminEmail",type:"email",autoComplete:"email",className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"<EMAIL>"})}}),_.adminEmail&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.adminEmail.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password *"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)(l.Qr,{name:"password",control:N,render:e=>{let{field:a}=e;return(0,t.jsx)("input",{...a,id:"password",type:o?"text":"password",autoComplete:"new-password",className:"block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Min 8 characters"})}}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>d(!o),"aria-label":o?"Hide password":"Show password",children:o?(0,t.jsxs)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}):(0,t.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.758 6.758M14.121 14.121l3.121 3.121M14.121 14.121L9.878 9.878m4.242 4.242L19.242 19.242"})})})]}),_.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:_.password.message}),(0,t.jsx)("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:"Password must contain: uppercase, lowercase, number, and special character"})]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("button",{type:"submit",disabled:a||!R||k,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:[a?(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),a?"Creating Account...":"Create Account"]})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",(0,t.jsx)(c(),{href:"/login",className:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300",children:"Sign in here"})]})})]})]}),(0,t.jsx)(Toast,{message:p.message,type:p.type,show:p.show,onClose:()=>y({...p,show:!1})})]})}x.Ry({success:x.O7(),message:x.Z_(),data:x.Ry({tenant:x.Ry({id:x.Z_(),name:x.Z_(),slug:x.Z_()}),user:x.Ry({id:x.Z_(),email:x.Z_()}),session:x.Ry({access_token:x.Z_(),refresh_token:x.Z_(),expires_in:x.Rx()})}).optional()}),x.Ry({success:x.O7().default(!1),error:x.Z_(),details:x.IM(x.IX(x.Z_())).optional()})},8007:function(e,a,r){"use strict";r.d(a,{M:function(){return useSlugify}});var t=r(2265);function useSlugify(){let[e,a]=(0,t.useState)(!1),r=(0,t.useCallback)(e=>e.toLowerCase().trim().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""),[]),s=(0,t.useCallback)(async e=>{if(!e||e.length<3)return!1;a(!0);try{let a=await fetch("/api/auth/validate-slug?slug=".concat(encodeURIComponent(e))),r=await a.json();return r.available}catch(e){return console.error("Slug validation error:",e),!1}finally{a(!1)}},[]),n=(0,t.useCallback)(e=>/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(e)&&e.length>=3&&e.length<=50,[]),l=["api","admin","app","www","mail","ftp","localhost","dashboard","billing","support","help","docs","blog","status","staging","dev","test","demo","cdn","assets","static","public","private","secure","internal","system","root","user","account","login","signup","auth","oauth","settings","profile","home","index","about","contact","terms","privacy","legal","pricing","features","enterprise"],i=(0,t.useCallback)(e=>l.includes(e.toLowerCase()),[]);return{generateSlug:r,validateSlugAvailability:s,isValidSlugFormat:n,isReservedSlug:i,isValidating:e}}},7341:function(e,a,r){"use strict";r.d(a,{OQ:function(){return s}});var t=r(4243);let createClient=()=>(0,t.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let s=getSupabaseClient()}},function(e){e.O(0,[155,326,509,727,971,472,744],function(){return e(e.s=886)}),_N_E=e.O()}]);