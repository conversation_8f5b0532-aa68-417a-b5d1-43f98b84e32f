(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[928],{6770:function(e,t,n){Promise.resolve().then(n.bind(n,4276))},4276:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return TestPage}});var s=n(7437),c=n(2265),r=n(7341);function TestPage(){let[e,t]=(0,c.useState)("Loading...");return(0,c.useEffect)(()=>{let testConnection=async()=>{try{let{data:e,error:n}=await r.OQ.auth.getSession();n?t("❌ Auth Error: ".concat(n.message)):t("✅ App is working! Supabase connected.")}catch(e){t("❌ Connection Error: ".concat(e))}};testConnection()},[]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md w-full text-center p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"BHEEMDINE Test Page"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Connection Status"}),(0,s.jsx)("p",{className:"text-gray-600",children:e})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("a",{href:"/",className:"block w-full bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700",children:"Go to Home"}),(0,s.jsx)("a",{href:"/login",className:"block w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700",children:"Go to Login"}),(0,s.jsx)("a",{href:"/signup",className:"block w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700",children:"Go to Signup"})]}),(0,s.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:[(0,s.jsxs)("p",{children:["Environment: ","production"]}),(0,s.jsxs)("p",{children:["Supabase URL: ","✅ Set"]})]})]})})}},7341:function(e,t,n){"use strict";n.d(t,{OQ:function(){return c}});var s=n(4243);let createClient=()=>(0,s.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let c=getSupabaseClient()}},function(e){e.O(0,[155,971,472,744],function(){return e(e.s=6770)}),_N_E=e.O()}]);