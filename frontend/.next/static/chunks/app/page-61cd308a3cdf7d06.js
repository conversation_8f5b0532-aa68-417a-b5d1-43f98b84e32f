(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{352:function(e,t,s){Promise.resolve().then(s.bind(s,5721))},5721:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return HomePage}});var r=s(7437),a=s(4033),n=s(1396),i=s.n(n);function HomePage(){return(0,a.useRouter)(),(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-indigo-100",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("header",{className:"py-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"TapDine"})}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(i(),{href:"/login",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"Sign In"}),(0,r.jsx)(i(),{href:"/signup",className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Get Started"})]})]})}),(0,r.jsxs)("main",{className:"py-20",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl",children:[(0,r.jsx)("span",{className:"block",children:"Digital Menu &"}),(0,r.jsx)("span",{className:"block text-primary-600",children:"Order Management"})]}),(0,r.jsx)("p",{className:"mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl",children:"Transform your restaurant with QR code menus, online ordering, and streamlined operations. Perfect for modern restaurants and cafes."}),(0,r.jsxs)("div",{className:"mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8",children:[(0,r.jsx)("div",{className:"rounded-md shadow",children:(0,r.jsx)(i(),{href:"/signup",className:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10",children:"Start Free Trial"})}),(0,r.jsx)("div",{className:"mt-3 rounded-md shadow sm:mt-0 sm:ml-3",children:(0,r.jsx)(i(),{href:"/login",className:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10",children:"Sign In"})})]})]}),(0,r.jsx)("div",{className:"mt-20",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"QR Code Menus"}),(0,r.jsx)("p",{className:"mt-2 text-base text-gray-500",children:"Contactless digital menus accessible via QR codes"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Order Management"}),(0,r.jsx)("p",{className:"mt-2 text-base text-gray-500",children:"Streamlined order processing and kitchen management"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 00-2 2h-2a2 2 0 00-2-2z"})})}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Analytics"}),(0,r.jsx)("p",{className:"mt-2 text-base text-gray-500",children:"Insights into sales, popular items, and customer behavior"})]})]})})]})]})})}},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(2265),a=Symbol.for("react.element"),n=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,d=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,m={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var r,n={},l=null,c=null;for(r in void 0!==s&&(l=""+s),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!m.hasOwnProperty(r)&&(n[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===n[r]&&(n[r]=t[r]);return{$$typeof:a,type:e,key:l,ref:c,props:n,_owner:d.current}}t.Fragment=n,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)},1396:function(e,t,s){e.exports=s(8326)},4033:function(e,t,s){e.exports=s(94)}},function(e){e.O(0,[326,971,472,744],function(){return e(e.s=352)}),_N_E=e.O()}]);