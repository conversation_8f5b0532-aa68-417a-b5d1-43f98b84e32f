(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{2473:function(e,t,s){Promise.resolve().then(s.bind(s,1318))},1318:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return SetupPage}});var a=s(7437),r=s(2265),n=s(4033),l=s(7341),i=s(8007);function SetupPage(){let e=(0,n.useRouter)(),[t,s]=(0,r.useState)(null),[o,u]=(0,r.useState)(!0),[d,c]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),[h,x]=(0,r.useState)({business_name:"",tenant_slug:"",business_type:"restaurant",phone:"",address:""}),{generateSlug:g,validateSlugAvailability:f,isValidSlugFormat:b}=(0,i.M)();(0,r.useEffect)(()=>{let checkAuth=async()=>{let{data:{session:t}}=await l.OQ.auth.getSession();if(!t){e.push("/login");return}if(s(t.user),t.user.user_metadata){let e=t.user.user_metadata;x(t=>({...t,business_name:e.full_name||e.name||""}))}u(!1)};checkAuth()},[e]),(0,r.useEffect)(()=>{if(h.business_name&&!h.tenant_slug){let e=g(h.business_name);x(t=>({...t,tenant_slug:e}))}},[h.business_name,g]);let handleChange=e=>{let{name:t,value:s}=e.target;x(e=>({...e,[t]:s}))},handleSubmit=async s=>{s.preventDefault(),c(!0),p(null);try{if(!b(h.tenant_slug))throw Error("Invalid tenant slug. Use only letters, numbers, and hyphens.");let s=await f(h.tenant_slug);if(!s)throw Error("This business name is already taken. Please choose another.");let{data:i,error:o}=await l.OQ.from("tenants").insert({name:h.business_name,slug:h.tenant_slug,email:t.email,phone:h.phone,address:h.address,status:"ACTIVE",settings:{business_type:h.business_type}}).select().single();if(console.log("Tenant creation result:",{tenant:i,tenantError:o}),o)throw o;let{data:u}=await l.OQ.from("users").select("id").eq("id",t.id).single();if(!u){var a,r,n;let{error:e}=await l.OQ.from("users").insert({id:t.id,email:t.email,first_name:(null===(a=t.user_metadata)||void 0===a?void 0:a.first_name)||(null===(r=t.user_metadata)||void 0===r?void 0:r.name)||"",last_name:(null===(n=t.user_metadata)||void 0===n?void 0:n.last_name)||""});e&&console.warn("User profile creation warning:",e)}let{error:d}=await l.OQ.from("user_tenants").insert({user_id:t.id,tenant_id:i.id,role:"OWNER",is_active:!0});if(d)throw d;e.push("/t/".concat(h.tenant_slug,"/dashboard"))}catch(t){console.error("Setup error details:",t);let e="Setup failed. Please try again.";t instanceof Error?e=t.message:"object"==typeof t&&null!==t&&(t.message?e=t.message:t.details&&(e="Database error: ".concat(t.details))),p(e)}finally{c(!1)}};return o?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Complete Your Setup"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Let's set up your restaurant on TapDine"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,a.jsxs)("form",{className:"space-y-6",onSubmit:handleSubmit,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"business_name",className:"block text-sm font-medium text-gray-700",children:"Business Name *"}),(0,a.jsx)("input",{id:"business_name",name:"business_name",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Your Restaurant Name",value:h.business_name,onChange:handleChange})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tenant_slug",className:"block text-sm font-medium text-gray-700",children:"URL Identifier *"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm",children:"tapdine.com/"}),(0,a.jsx)("input",{id:"tenant_slug",name:"tenant_slug",type:"text",required:!0,className:"flex-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-none rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"your-restaurant",value:h.tenant_slug,onChange:e=>{let t=g(e.target.value);x(e=>({...e,tenant_slug:t}))}})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This will be your unique URL. Only letters, numbers, and hyphens allowed."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"business_type",className:"block text-sm font-medium text-gray-700",children:"Business Type"}),(0,a.jsxs)("select",{id:"business_type",name:"business_type",className:"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",value:h.business_type,onChange:handleChange,children:[(0,a.jsx)("option",{value:"restaurant",children:"Restaurant"}),(0,a.jsx)("option",{value:"cafe",children:"Cafe"}),(0,a.jsx)("option",{value:"bar",children:"Bar"}),(0,a.jsx)("option",{value:"food_truck",children:"Food Truck"}),(0,a.jsx)("option",{value:"bakery",children:"Bakery"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,a.jsx)("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"+****************",value:h.phone,onChange:handleChange})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address"}),(0,a.jsx)("input",{id:"address",name:"address",type:"text",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"123 Main St, City, State 12345",value:h.address,onChange:handleChange})]}),m&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:m})}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{type:"submit",disabled:d,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[d?(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):null,"Complete Setup"]})})]})})})]})}},8007:function(e,t,s){"use strict";s.d(t,{M:function(){return useSlugify}});var a=s(2265);function useSlugify(){let[e,t]=(0,a.useState)(!1),s=(0,a.useCallback)(e=>e.toLowerCase().trim().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""),[]),r=(0,a.useCallback)(async e=>{if(!e||e.length<3)return!1;t(!0);try{let t=await fetch("/api/auth/validate-slug?slug=".concat(encodeURIComponent(e))),s=await t.json();return s.available}catch(e){return console.error("Slug validation error:",e),!1}finally{t(!1)}},[]),n=(0,a.useCallback)(e=>/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(e)&&e.length>=3&&e.length<=50,[]),l=["api","admin","app","www","mail","ftp","localhost","dashboard","billing","support","help","docs","blog","status","staging","dev","test","demo","cdn","assets","static","public","private","secure","internal","system","root","user","account","login","signup","auth","oauth","settings","profile","home","index","about","contact","terms","privacy","legal","pricing","features","enterprise"],i=(0,a.useCallback)(e=>l.includes(e.toLowerCase()),[]);return{generateSlug:s,validateSlugAvailability:r,isValidSlugFormat:n,isReservedSlug:i,isValidating:e}}},7341:function(e,t,s){"use strict";s.d(t,{OQ:function(){return r}});var a=s(4243);let createClient=()=>(0,a.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let r=getSupabaseClient()},4033:function(e,t,s){e.exports=s(94)}},function(e){e.O(0,[155,971,472,744],function(){return e(e.s=2473)}),_N_E=e.O()}]);