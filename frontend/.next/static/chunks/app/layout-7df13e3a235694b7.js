(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{6908:function(t,e,n){Promise.resolve().then(n.t.bind(n,2489,23)),Promise.resolve().then(n.bind(n,6785))},6785:function(t,e,n){"use strict";n.r(e),n.d(e,{TenantProvider:function(){return TenantProvider},useTenant:function(){return useTenant}});var r=n(7437),a=n(2265),i=n(4033),s=n(7341);let o=["ACTIVE","SUSPENDED","INACTIVE","PENDING"],u=["OWNER","ADMIN","MANAGER","STAFF","READONLY"];function isTenantStatus(t){return"string"==typeof t&&o.includes(t)}function isUserRole(t){return"string"==typeof t&&u.includes(t)}function isValidTenant(t){return!!t&&"object"==typeof t&&"string"==typeof t.id&&"string"==typeof t.name&&"string"==typeof t.slug&&isTenantStatus(t.status)}function isValidUser(t){return!!t&&"object"==typeof t&&"string"==typeof t.id&&"string"==typeof t.email&&(void 0===t.role||isUserRole(t.role))}function normalizeTenantStatus(t){if("string"==typeof t){let e=t.toUpperCase();if(isTenantStatus(e))return e}return console.warn("Invalid tenant status received:",t,"defaulting to ACTIVE"),"ACTIVE"}function normalizeUserRole(t){if("string"==typeof t){let e=t.toUpperCase();if(isUserRole(e))return e}return console.warn("Invalid user role received:",t,"defaulting to STAFF"),"STAFF"}let l=(0,a.createContext)(void 0);function TenantProvider(t){let{children:e,initialTenant:n=null}=t,[o,u]=(0,a.useState)(n),[c,d]=(0,a.useState)(null),[f,p]=(0,a.useState)(!0),[h,T]=(0,a.useState)(null),g=(0,i.useRouter)();return(0,a.useEffect)(()=>{let t=!0;async function loadTenantAndUser(){try{p(!0),T(null);let{data:{session:e},error:r}=await s.OQ.auth.getSession();if(r)throw r;if(!e){t&&(d(null),u(null),p(!1));return}let a=getTenantSlug();if(!a&&!n){t&&(T(Error("No tenant context available")),p(!1));return}let{data:i,error:o}=await s.OQ.from("users").select("\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          ").eq("id",e.user.id).single();if(o)throw o;let l=i.user_tenants.find(t=>t.tenant.slug===(a||(null==n?void 0:n.slug)));if(!l)throw Error("User does not have access to this tenant");if(t){let t={...l.tenant,status:normalizeTenantStatus(l.tenant.status)},e={id:i.id,email:i.email,tenant_id:l.tenant.id,role:normalizeUserRole(l.role),first_name:i.first_name,last_name:i.last_name,avatar_url:i.avatar_url,created_at:i.created_at,updated_at:i.updated_at};if(isValidTenant(t)&&isValidUser(e))d(e),u(t);else throw Error("Invalid tenant or user data received");p(!1)}}catch(e){console.error("Error loading tenant context:",e),t&&(T(e),p(!1))}}loadTenantAndUser();let{data:{subscription:e}}=s.OQ.auth.onAuthStateChange(async(t,e)=>{"SIGNED_IN"===t||"TOKEN_REFRESHED"===t?loadTenantAndUser():"SIGNED_OUT"===t&&(d(null),u(null),g.push("/login"))});return()=>{t=!1,e.unsubscribe()}},[g,n]),(0,r.jsx)(l.Provider,{value:{tenant:o,user:c,isLoading:f,error:h},children:e})}function useTenant(){let t=(0,a.useContext)(l);if(void 0===t)throw Error("useTenant must be used within a TenantProvider");return t}function getTenantSlug(){let t=window.location.hostname,e=t.split(".")[0];if(e&&"www"!==e&&"localhost"!==e)return e;let n=window.location.pathname.match(/^\/t\/([^\/]+)/);if(n)return n[1];let r=new URLSearchParams(window.location.search),a=r.get("tenant");if(a)return a;let i=document.cookie.split("; ").find(t=>t.startsWith("tenant-slug="));return i?i.split("=")[1]:null}},7341:function(t,e,n){"use strict";n.d(e,{OQ:function(){return a}});var r=n(4243);let createClient=()=>(0,r.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let a=getSupabaseClient()},2489:function(){},4033:function(t,e,n){t.exports=n(94)}},function(t){t.O(0,[155,971,472,744],function(){return t(t.s=6908)}),_N_E=t.O()}]);