(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[728],{1829:function(e,t,r){Promise.resolve().then(r.bind(r,8581)),Promise.resolve().then(r.bind(r,5869)),Promise.resolve().then(r.bind(r,6785))},8581:function(e,t,r){"use strict";r.r(t),r.d(t,{ProtectedRoute:function(){return ProtectedRoute},withAuth:function(){return withAuth}});var a=r(7437),n=r(2265),s=r(4033),i=r(6785);let o={STAFF:1,MANAGER:2,ADMIN:3,OWNER:4};function ProtectedRoute(e){let{children:t,requiredRole:r,requiredPermissions:o,fallback:l}=e,{user:d,tenant:c,isLoading:u,error:h}=(0,i.useTenant)(),m=(0,s.useRouter)();return((0,n.useEffect)(()=>{if(!u&&!d){let e=window.location.pathname,t="/login?returnUrl=".concat(encodeURIComponent(e));m.push(t)}},[d,u,m]),u)?l||(0,a.jsx)("div",{children:"Loading..."}):h?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-600",children:"Error"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:h.message}),(0,a.jsx)("button",{onClick:()=>m.push("/login"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Go to Login"})]}):d&&c?"ACTIVE"!==c.status?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-600",children:"Tenant Suspended"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"This organization's account is currently suspended."}),(0,a.jsx)("button",{onClick:()=>m.push("/login"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Switch Organization"})]}):r&&!hasRequiredRole(d,r)||o&&!hasRequiredPermissions(d,o)?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-600",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"You don't have the required permissions to access this page."}),(0,a.jsx)("button",{onClick:()=>m.push("/dashboard"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Go to Dashboard"})]}):(0,a.jsx)(a.Fragment,{children:t}):null}function hasRequiredRole(e,t){let r=o[e.role]||0,a=o[t]||0;return r>=a}function hasRequiredPermissions(e,t){let r={OWNER:["*"],ADMIN:["read","write","delete","manage_users","manage_settings"],MANAGER:["read","write","delete"],STAFF:["read","write"]}[e.role]||[];return t.every(e=>r.includes("*")||r.includes(e))}function withAuth(e,t){return function(r){return(0,a.jsx)(ProtectedRoute,{requiredRole:null==t?void 0:t.requiredRole,requiredPermissions:null==t?void 0:t.requiredPermissions,fallback:null==t?void 0:t.fallback,children:(0,a.jsx)(e,{...r})})}}},5869:function(e,t,r){"use strict";r.r(t),r.d(t,{DashboardLayout:function(){return DashboardLayout}});var a=r(7437),n=r(2265),s=r(4033),i=r(6691),o=r.n(i),l=r(1396),d=r.n(l),c=r(5243);function TopNavigation(e){var t;let{onMenuClick:r,darkMode:s,onDarkModeToggle:i,tenant:l}=e,[u,h]=(0,n.useState)(!1),{user:m,signOut:g}=(0,c.a)(),x=(0,n.useRef)(null);(0,n.useEffect)(()=>{let handleClickOutside=e=>{x.current&&!x.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>document.removeEventListener("mousedown",handleClickOutside)},[]);let handleLogout=async()=>{try{await g()}catch(e){console.error("Logout error:",e)}};return(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",role:"banner",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 lg:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{type:"button",className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 lg:hidden",onClick:r,"aria-label":"Open sidebar","aria-expanded":"false",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,a.jsxs)("div",{className:"flex items-center ml-2 lg:hidden",children:[(0,a.jsxs)(d(),{href:"/",className:"flex items-center",children:[(0,a.jsx)(o(),{src:"/logo.svg",alt:"TapDine",width:32,height:32,className:"h-8 w-8"}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"TapDine"})]}),l&&(0,a.jsx)("div",{className:"ml-4 pl-4 border-l border-gray-300 dark:border-gray-600",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:l.name})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{type:"button",onClick:i,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 dark:hover:text-gray-200","aria-label":s?"Switch to light mode":"Switch to dark mode",children:s?(0,a.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}):(0,a.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})}),(0,a.jsxs)("div",{className:"relative",ref:x,children:[(0,a.jsxs)("button",{type:"button",className:"flex items-center max-w-xs bg-gray-100 dark:bg-gray-700 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800",onClick:()=>h(!u),"aria-label":"User menu","aria-expanded":u,"aria-haspopup":"true",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open user menu"}),(null==m?void 0:m.avatar_url)?(0,a.jsx)(o(),{className:"h-8 w-8 rounded-full",src:m.avatar_url,alt:"".concat(m.first_name," ").concat(m.last_name),width:32,height:32}):(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:(null==m?void 0:m.first_name)&&(null==m?void 0:m.last_name)?"".concat(m.first_name.charAt(0)).concat(m.last_name.charAt(0)):(null==m?void 0:null===(t=m.email)||void 0===t?void 0:t.charAt(0).toUpperCase())||"U"})})]}),u&&(0,a.jsx)("div",{className:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"font-medium truncate",children:[null==m?void 0:m.first_name," ",null==m?void 0:m.last_name]}),(0,a.jsx)("div",{className:"text-gray-500 dark:text-gray-400 truncate",children:null==m?void 0:m.email})]}),(0,a.jsx)(d(),{href:"/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>h(!1),children:"Your Profile"}),(0,a.jsx)(d(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>h(!1),children:"Settings"}),(0,a.jsx)("button",{type:"button",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:handleLogout,children:"Sign out"})]})})]})]})]})})}let u=[{name:"Dashboard",href:"/dashboard",icon:(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v14l-5-3-5 3V5z"})]})},{name:"Menu",href:"/menu",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},{name:"Orders",href:"/orders",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{name:"Staff",href:"/staff",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})},{name:"Settings",href:"/settings",icon:(0,a.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}];function Sidebar(e){let{isOpen:t,onClose:r,tenant:n}=e,i=(0,s.usePathname)(),getTenantHref=e=>(null==n?void 0:n.slug)?"/t/".concat(n.slug).concat(e):e,isActiveRoute=e=>{let t=getTenantHref(e);return i===t||i.startsWith(t+"/")};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ".concat(t?"translate-x-0":"-translate-x-full"),children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o(),{src:"/logo.svg",alt:"TapDine",width:32,height:32,className:"h-8 w-8"}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"TapDine"})]}),(0,a.jsx)("button",{type:"button",className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 lg:hidden",onClick:r,"aria-label":"Close sidebar",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsx)("nav",{className:"flex-1 px-2 py-4 space-y-1 overflow-y-auto",role:"navigation","aria-label":"Main navigation",children:u.map(e=>{let t=isActiveRoute(e.href);return(0,a.jsxs)(d(),{href:getTenantHref(e.href),className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ".concat(t?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"),onClick:r,"aria-current":t?"page":void 0,children:[(0,a.jsx)("span",{className:"mr-3 flex-shrink-0 transition-colors duration-200 ".concat(t?"text-primary-500 dark:text-primary-300":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"),children:e.icon}),e.name]},e.name)})}),n&&(0,a.jsx)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:n.logo_url?(0,a.jsx)(o(),{src:n.logo_url,alt:n.name,width:32,height:32,className:"h-8 w-8 rounded-full"}):(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-primary-600 dark:text-primary-300",children:n.name.charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"ml-3 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:n.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:n.slug})]})]})})]})})})}function MobileOverlay(e){let{isOpen:t,onClose:r}=e;return t?(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity duration-300 ease-linear lg:hidden",onClick:r,"aria-hidden":"true"}):null}var h=r(6785);function DashboardLayout(e){let{children:t}=e,[r,i]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),d=(0,s.usePathname)(),{tenant:c}=(0,h.useTenant)();return(0,n.useEffect)(()=>{let e="true"===localStorage.getItem("darkMode");l(e),e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[]),(0,n.useEffect)(()=>{i(!1)},[d]),(0,a.jsxs)("div",{className:"flex h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(MobileOverlay,{isOpen:r,onClose:()=>{i(!1)}}),(0,a.jsx)(Sidebar,{isOpen:r,onClose:()=>i(!1),tenant:c}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden lg:ml-64",children:[(0,a.jsx)(TopNavigation,{onMenuClick:()=>i(!0),darkMode:o,onDarkModeToggle:()=>{let e=!o;l(e),localStorage.setItem("darkMode",e.toString()),e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},tenant:c}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 lg:p-6",role:"main","aria-label":"Main content",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:t})})]})]})}},6785:function(e,t,r){"use strict";r.r(t),r.d(t,{TenantProvider:function(){return TenantProvider},useTenant:function(){return useTenant}});var a=r(7437),n=r(2265),s=r(4033),i=r(7341);let o=["ACTIVE","SUSPENDED","INACTIVE","PENDING"],l=["OWNER","ADMIN","MANAGER","STAFF","READONLY"];function isTenantStatus(e){return"string"==typeof e&&o.includes(e)}function isUserRole(e){return"string"==typeof e&&l.includes(e)}function isValidTenant(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.name&&"string"==typeof e.slug&&isTenantStatus(e.status)}function isValidUser(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.email&&(void 0===e.role||isUserRole(e.role))}function normalizeTenantStatus(e){if("string"==typeof e){let t=e.toUpperCase();if(isTenantStatus(t))return t}return console.warn("Invalid tenant status received:",e,"defaulting to ACTIVE"),"ACTIVE"}function normalizeUserRole(e){if("string"==typeof e){let t=e.toUpperCase();if(isUserRole(t))return t}return console.warn("Invalid user role received:",e,"defaulting to STAFF"),"STAFF"}let d=(0,n.createContext)(void 0);function TenantProvider(e){let{children:t,initialTenant:r=null}=e,[o,l]=(0,n.useState)(r),[c,u]=(0,n.useState)(null),[h,m]=(0,n.useState)(!0),[g,x]=(0,n.useState)(null),f=(0,s.useRouter)();return(0,n.useEffect)(()=>{let e=!0;async function loadTenantAndUser(){try{m(!0),x(null);let{data:{session:t},error:a}=await i.OQ.auth.getSession();if(a)throw a;if(!t){e&&(u(null),l(null),m(!1));return}let n=getTenantSlug();if(!n&&!r){e&&(x(Error("No tenant context available")),m(!1));return}let{data:s,error:o}=await i.OQ.from("users").select("\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          ").eq("id",t.user.id).single();if(o)throw o;let d=s.user_tenants.find(e=>e.tenant.slug===(n||(null==r?void 0:r.slug)));if(!d)throw Error("User does not have access to this tenant");if(e){let e={...d.tenant,status:normalizeTenantStatus(d.tenant.status)},t={id:s.id,email:s.email,tenant_id:d.tenant.id,role:normalizeUserRole(d.role),first_name:s.first_name,last_name:s.last_name,avatar_url:s.avatar_url,created_at:s.created_at,updated_at:s.updated_at};if(isValidTenant(e)&&isValidUser(t))u(t),l(e);else throw Error("Invalid tenant or user data received");m(!1)}}catch(t){console.error("Error loading tenant context:",t),e&&(x(t),m(!1))}}loadTenantAndUser();let{data:{subscription:t}}=i.OQ.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?loadTenantAndUser():"SIGNED_OUT"===e&&(u(null),l(null),f.push("/login"))});return()=>{e=!1,t.unsubscribe()}},[f,r]),(0,a.jsx)(d.Provider,{value:{tenant:o,user:c,isLoading:h,error:g},children:t})}function useTenant(){let e=(0,n.useContext)(d);if(void 0===e)throw Error("useTenant must be used within a TenantProvider");return e}function getTenantSlug(){let e=window.location.hostname,t=e.split(".")[0];if(t&&"www"!==t&&"localhost"!==t)return t;let r=window.location.pathname.match(/^\/t\/([^\/]+)/);if(r)return r[1];let a=new URLSearchParams(window.location.search),n=a.get("tenant");if(n)return n;let s=document.cookie.split("; ").find(e=>e.startsWith("tenant-slug="));return s?s.split("=")[1]:null}},5243:function(e,t,r){"use strict";r.d(t,{a:function(){return useAuth}});var a=r(2265),n=r(4033),s=r(7341),i=r(6785);function useAuth(){let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)(null),l=(0,n.useRouter)(),{tenant:d,user:c}=(0,i.useTenant)(),signIn=async e=>{try{t(!0),o(null);let{data:r,error:a}=await s.OQ.auth.signInWithPassword({email:e.email,password:e.password});if(a)throw a;if(e.tenant_slug){let t=await verifyTenantAccess(r.user.id,e.tenant_slug);if(!t)throw Error("Access denied to this tenant")}let n=new URLSearchParams(window.location.search).get("returnUrl")||"/dashboard";return l.push(n),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signUp=async e=>{try{t(!0),o(null);let{data:r,error:a}=await s.OQ.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.first_name,last_name:e.last_name}}});if(a)throw a;return r.user&&!e.tenant_slug&&await createTenant(r.user.id,e),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signOut=async()=>{try{t(!0);let{error:e}=await s.OQ.auth.signOut();if(e)throw e;l.push("/login")}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},resetPassword=async e=>{try{t(!0),o(null);let{error:r}=await s.OQ.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},updatePassword=async e=>{try{t(!0),o(null);let{error:r}=await s.OQ.auth.updateUser({password:e});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}};return{user:c,tenant:d,loading:e,error:r,signIn,signUp,signOut,resetPassword,updatePassword}}async function verifyTenantAccess(e,t){try{let{data:r,error:a}=await s.OQ.from("user_tenants").select("tenant_id, tenants!inner(slug, status)").eq("user_id",e).eq("tenants.slug",t).eq("tenants.status","ACTIVE").single();return!a&&!!r}catch(e){return!1}}async function createTenant(e,t){try{let r=t.tenant_name.toLowerCase().replace(/[^a-z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""),{data:a,error:n}=await s.OQ.from("tenants").insert({name:t.tenant_name,slug:r,email:t.email,status:"ACTIVE"}).select().single();if(n)throw n;let{error:i}=await s.OQ.from("users").insert({id:e,email:t.email,first_name:t.first_name,last_name:t.last_name});if(i)throw i;let{error:o}=await s.OQ.from("user_tenants").insert({user_id:e,tenant_id:a.id,role:"OWNER"});if(o)throw o;return a}catch(e){throw console.error("Error creating tenant:",e),e}}},7341:function(e,t,r){"use strict";r.d(t,{OQ:function(){return n}});var a=r(4243);let createClient=()=>(0,a.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let n=getSupabaseClient()}},function(e){e.O(0,[155,326,509,971,472,744],function(){return e(e.s=1829)}),_N_E=e.O()}]);