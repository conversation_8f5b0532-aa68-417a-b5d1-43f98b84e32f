(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[659,145,344,421],{244:function(e,r,t){Promise.resolve().then(t.bind(t,5127))},5127:function(e,r,t){"use strict";t.r(r),t.d(r,{PageHeader:function(){return PageHeader}});var s=t(7437);function PageHeader(e){let{title:r,subtitle:t,actions:a,breadcrumbs:n}=e;return(0,s.jsxs)("div",{className:"mb-6",children:[n&&n.length>0&&(0,s.jsx)("nav",{className:"mb-4","aria-label":"Breadcrumb",children:(0,s.jsx)("ol",{className:"flex items-center space-x-2 text-sm",children:n.map((e,r)=>(0,s.jsxs)("li",{className:"flex items-center",children:[r>0&&(0,s.jsx)("svg",{className:"w-4 h-4 mx-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.href?(0,s.jsx)("a",{href:e.href,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:e.label}):(0,s.jsx)("span",{className:"text-gray-900 dark:text-white font-medium",children:e.label})]},r))})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight",children:r}),t&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:t})]}),a&&(0,s.jsx)("div",{className:"mt-4 flex-shrink-0 sm:mt-0 sm:ml-4",children:a})]})]})}t(2265)},622:function(e,r,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=t(2265),a=Symbol.for("react.element"),n=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function q(e,r,t){var s,n={},o=null,m=null;for(s in void 0!==t&&(o=""+t),void 0!==r.key&&(o=""+r.key),void 0!==r.ref&&(m=r.ref),r)l.call(r,s)&&!c.hasOwnProperty(s)&&(n[s]=r[s]);if(e&&e.defaultProps)for(s in r=e.defaultProps)void 0===n[s]&&(n[s]=r[s]);return{$$typeof:a,type:e,key:o,ref:m,props:n,_owner:i.current}}r.Fragment=n,r.jsx=q,r.jsxs=q},7437:function(e,r,t){"use strict";e.exports=t(622)}},function(e){e.O(0,[971,472,744],function(){return e(e.s=244)}),_N_E=e.O()}]);