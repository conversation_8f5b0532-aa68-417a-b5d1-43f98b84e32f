(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{7302:function(e,t,r){Promise.resolve().then(r.bind(r,6287))},6287:function(e,t,r){"use strict";r.r(t),r.d(t,{LoginForm:function(){return LoginForm}});var n=r(7437),a=r(2265),s=r(4033),i=r(5243),o=r(7341);function GoogleAuthButton(e){let{mode:t,disabled:r=!1,onError:s}=e,[i,l]=(0,a.useState)(!1),handleGoogleAuth=async()=>{try{l(!0);let{data:e,error:r}=await o.OQ.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback?type=").concat(t),queryParams:{access_type:"offline",prompt:"consent"}}});r&&(console.error("Google OAuth error:",r),null==s||s(r.message),l(!1))}catch(e){console.error("Google auth failed:",e),null==s||s("Failed to initiate Google authentication"),l(!1)}};return(0,n.jsxs)("button",{type:"button",onClick:handleGoogleAuth,disabled:r||i,className:"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:[i?(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,n.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,n.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,n.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,n.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),i?"Connecting...":"Continue with Google"]})}function LoginForm(){let[e,t]=(0,a.useState)({email:"",password:"",tenant_slug:""}),[r,o]=(0,a.useState)(!1),[l,c]=(0,a.useState)(!1),{signIn:u,loading:d,error:m}=(0,i.a)(),h=(0,s.useRouter)(),f=(0,s.useSearchParams)();(0,a.useEffect)(()=>{let e=f.get("tenant");e&&t(t=>({...t,tenant_slug:e}))},[f]);let handleSubmit=async t=>{t.preventDefault(),c(!0);try{await u(e)}catch(e){}finally{c(!1)}},handleChange=e=>{let{name:r,value:n}=e.target;t(e=>({...e,[r]:n}))};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,n.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,n.jsx)("button",{onClick:()=>h.push("/signup"),className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign up"})]})]}),(0,n.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:handleSubmit,children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"tenant_slug",className:"block text-sm font-medium text-gray-700",children:"Organization ID"}),(0,n.jsx)("input",{id:"tenant_slug",name:"tenant_slug",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"your-organization",value:e.tenant_slug,onChange:handleChange}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This is your organization's unique identifier"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,n.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"<EMAIL>",value:e.email,onChange:handleChange})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,n.jsxs)("div",{className:"mt-1 relative",children:[(0,n.jsx)("input",{id:"password",name:"password",type:r?"text":"password",autoComplete:"current-password",required:!0,className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password",value:e.password,onChange:handleChange}),(0,n.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>o(!r),children:r?(0,n.jsxs)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}):(0,n.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.758 6.758M14.121 14.121l3.121 3.121M14.121 14.121L9.878 9.878m4.242 4.242L19.242 19.242"})})})]})]})]}),m&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("p",{className:"text-sm text-red-700",children:m})})]})}),(0,n.jsx)("div",{children:(0,n.jsx)(GoogleAuthButton,{mode:"login",disabled:l||d,onError:e=>console.error("Google auth error:",e)})}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,n.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Or continue with email"})})]}),(0,n.jsx)("div",{children:(0,n.jsxs)("button",{type:"submit",disabled:l||d,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[l||d?(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):null,"Sign in with Email"]})}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("button",{type:"button",onClick:()=>h.push("/forgot-password"),className:"text-sm text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})]})]})})}},6785:function(e,t,r){"use strict";r.r(t),r.d(t,{TenantProvider:function(){return TenantProvider},useTenant:function(){return useTenant}});var n=r(7437),a=r(2265),s=r(4033),i=r(7341);let o=["ACTIVE","SUSPENDED","INACTIVE","PENDING"],l=["OWNER","ADMIN","MANAGER","STAFF","READONLY"];function isTenantStatus(e){return"string"==typeof e&&o.includes(e)}function isUserRole(e){return"string"==typeof e&&l.includes(e)}function isValidTenant(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.name&&"string"==typeof e.slug&&isTenantStatus(e.status)}function isValidUser(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.email&&(void 0===e.role||isUserRole(e.role))}function normalizeTenantStatus(e){if("string"==typeof e){let t=e.toUpperCase();if(isTenantStatus(t))return t}return console.warn("Invalid tenant status received:",e,"defaulting to ACTIVE"),"ACTIVE"}function normalizeUserRole(e){if("string"==typeof e){let t=e.toUpperCase();if(isUserRole(t))return t}return console.warn("Invalid user role received:",e,"defaulting to STAFF"),"STAFF"}let c=(0,a.createContext)(void 0);function TenantProvider(e){let{children:t,initialTenant:r=null}=e,[o,l]=(0,a.useState)(r),[u,d]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0),[f,p]=(0,a.useState)(null),g=(0,s.useRouter)();return(0,a.useEffect)(()=>{let e=!0;async function loadTenantAndUser(){try{h(!0),p(null);let{data:{session:t},error:n}=await i.OQ.auth.getSession();if(n)throw n;if(!t){e&&(d(null),l(null),h(!1));return}let a=getTenantSlug();if(!a&&!r){e&&(p(Error("No tenant context available")),h(!1));return}let{data:s,error:o}=await i.OQ.from("users").select("\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          ").eq("id",t.user.id).single();if(o)throw o;let c=s.user_tenants.find(e=>e.tenant.slug===(a||(null==r?void 0:r.slug)));if(!c)throw Error("User does not have access to this tenant");if(e){let e={...c.tenant,status:normalizeTenantStatus(c.tenant.status)},t={id:s.id,email:s.email,tenant_id:c.tenant.id,role:normalizeUserRole(c.role),first_name:s.first_name,last_name:s.last_name,avatar_url:s.avatar_url,created_at:s.created_at,updated_at:s.updated_at};if(isValidTenant(e)&&isValidUser(t))d(t),l(e);else throw Error("Invalid tenant or user data received");h(!1)}}catch(t){console.error("Error loading tenant context:",t),e&&(p(t),h(!1))}}loadTenantAndUser();let{data:{subscription:t}}=i.OQ.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?loadTenantAndUser():"SIGNED_OUT"===e&&(d(null),l(null),g.push("/login"))});return()=>{e=!1,t.unsubscribe()}},[g,r]),(0,n.jsx)(c.Provider,{value:{tenant:o,user:u,isLoading:m,error:f},children:t})}function useTenant(){let e=(0,a.useContext)(c);if(void 0===e)throw Error("useTenant must be used within a TenantProvider");return e}function getTenantSlug(){let e=window.location.hostname,t=e.split(".")[0];if(t&&"www"!==t&&"localhost"!==t)return t;let r=window.location.pathname.match(/^\/t\/([^\/]+)/);if(r)return r[1];let n=new URLSearchParams(window.location.search),a=n.get("tenant");if(a)return a;let s=document.cookie.split("; ").find(e=>e.startsWith("tenant-slug="));return s?s.split("=")[1]:null}},5243:function(e,t,r){"use strict";r.d(t,{a:function(){return useAuth}});var n=r(2265),a=r(4033),s=r(7341),i=r(6785);function useAuth(){let[e,t]=(0,n.useState)(!1),[r,o]=(0,n.useState)(null),l=(0,a.useRouter)(),{tenant:c,user:u}=(0,i.useTenant)(),signIn=async e=>{try{t(!0),o(null);let{data:r,error:n}=await s.OQ.auth.signInWithPassword({email:e.email,password:e.password});if(n)throw n;if(e.tenant_slug){let t=await verifyTenantAccess(r.user.id,e.tenant_slug);if(!t)throw Error("Access denied to this tenant")}let a=new URLSearchParams(window.location.search).get("returnUrl")||"/dashboard";return l.push(a),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signUp=async e=>{try{t(!0),o(null);let{data:r,error:n}=await s.OQ.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.first_name,last_name:e.last_name}}});if(n)throw n;return r.user&&!e.tenant_slug&&await createTenant(r.user.id,e),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signOut=async()=>{try{t(!0);let{error:e}=await s.OQ.auth.signOut();if(e)throw e;l.push("/login")}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},resetPassword=async e=>{try{t(!0),o(null);let{error:r}=await s.OQ.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},updatePassword=async e=>{try{t(!0),o(null);let{error:r}=await s.OQ.auth.updateUser({password:e});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}};return{user:u,tenant:c,loading:e,error:r,signIn,signUp,signOut,resetPassword,updatePassword}}async function verifyTenantAccess(e,t){try{let{data:r,error:n}=await s.OQ.from("user_tenants").select("tenant_id, tenants!inner(slug, status)").eq("user_id",e).eq("tenants.slug",t).eq("tenants.status","ACTIVE").single();return!n&&!!r}catch(e){return!1}}async function createTenant(e,t){try{let r=t.tenant_name.toLowerCase().replace(/[^a-z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""),{data:n,error:a}=await s.OQ.from("tenants").insert({name:t.tenant_name,slug:r,email:t.email,status:"ACTIVE"}).select().single();if(a)throw a;let{error:i}=await s.OQ.from("users").insert({id:e,email:t.email,first_name:t.first_name,last_name:t.last_name});if(i)throw i;let{error:o}=await s.OQ.from("user_tenants").insert({user_id:e,tenant_id:n.id,role:"OWNER"});if(o)throw o;return n}catch(e){throw console.error("Error creating tenant:",e),e}}},7341:function(e,t,r){"use strict";r.d(t,{OQ:function(){return a}});var n=r(4243);let createClient=()=>(0,n.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let a=getSupabaseClient()},4033:function(e,t,r){e.exports=r(94)}},function(e){e.O(0,[155,971,472,744],function(){return e(e.s=7302)}),_N_E=e.O()}]);