(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[453],{9934:function(e,t,r){Promise.resolve().then(r.bind(r,8553))},8553:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return AuthCallbackPage}});var s=r(7437),n=r(2265),a=r(4033),i=r(7341);function AuthCallbackPage(){let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),[r,c]=(0,n.useState)("loading"),[o,l]=(0,n.useState)("Processing authentication...");return(0,n.useEffect)(()=>{let handleAuthCallback=async()=>{try{let r=new URLSearchParams(window.location.search),s=r.get("code");if(s){let{data:r,error:n}=await i.OQ.auth.exchangeCodeForSession(s);if(n){console.error("Code exchange error:",n),c("error"),l("Authentication failed. Please try again.");return}if(!r.session){c("error"),l("No session created. Please try again.");return}let a=r.session.user,o=t.get("type")||"login";if("signup"===o)c("success"),l("Please complete your restaurant setup..."),e.push("/setup");else{let{data:t,error:r}=await i.OQ.from("user_tenants").select("\n                tenant_id,\n                role,\n                tenants!inner(slug, name, status)\n              ").eq("user_id",a.id).eq("is_active",!0).eq("tenants.status","ACTIVE");if(r){console.error("User tenant lookup error:",r),c("error"),l("Failed to load your account. Please try again.");return}if(!t||0===t.length){c("success"),l("Setting up your account..."),e.push("/setup");return}let s=t[0];c("success"),l("Redirecting to your dashboard..."),e.push("/t/".concat(s.tenants.slug,"/dashboard"))}}else{let{data:t,error:r}=await i.OQ.auth.getSession();if(r||!t.session){c("error"),l("Authentication failed. Please try signing in again.");return}c("success"),l("Redirecting..."),e.push("/dashboard")}}catch(e){console.error("Auth callback error:",e),c("error"),l("An unexpected error occurred. Please try again.")}};handleAuthCallback()},[e,t]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"max-w-md w-full text-center",children:(0,s.jsxs)("div",{className:"mb-6",children:["loading"===r&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("svg",{className:"animate-spin h-10 w-10 text-primary-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),"success"===r&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("svg",{className:"h-10 w-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),"error"===r&&(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("svg",{className:"h-10 w-10 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.982 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:o}),"error"===r&&(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,s.jsx)("button",{onClick:()=>e.push("/login"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Back to Login"}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{onClick:()=>e.push("/signup"),className:"text-primary-600 hover:text-primary-500 text-sm font-medium",children:"Create a new account"})})]})]})})})}},7341:function(e,t,r){"use strict";r.d(t,{OQ:function(){return n}});var s=r(4243);let createClient=()=>(0,s.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let n=getSupabaseClient()},4033:function(e,t,r){e.exports=r(94)}},function(e){e.O(0,[155,971,472,744],function(){return e(e.s=9934)}),_N_E=e.O()}]);