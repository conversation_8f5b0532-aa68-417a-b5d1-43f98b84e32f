(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[949],{6949:function(e,t,n){"use strict";n.r(t),n.d(t,{DashboardContent:function(){return DashboardContent}});var r=n(7437),a=n(6785);function DashboardContent(){let{tenant:e,user:t}=(0,a.useTenant)();return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"md:flex md:items-center md:justify-between",children:(0,r.jsx)("div",{className:"min-w-0 flex-1",children:(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight",children:"Dashboard"})})}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Orders"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"142"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Revenue"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"$12,426"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Growth"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"+12.3%"})]})})]})})})]}),t&&(0,r.jsxs)("div",{className:"mt-6 bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:["Welcome back, ",t.first_name||t.email,"!"]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:e&&"Managing ".concat(e.name)})]})]})}},6785:function(e,t,n){"use strict";n.r(t),n.d(t,{TenantProvider:function(){return TenantProvider},useTenant:function(){return useTenant}});var r=n(7437),a=n(2265),s=n(4033),i=n(7341);let l=["ACTIVE","SUSPENDED","INACTIVE","PENDING"],d=["OWNER","ADMIN","MANAGER","STAFF","READONLY"];function isTenantStatus(e){return"string"==typeof e&&l.includes(e)}function isUserRole(e){return"string"==typeof e&&d.includes(e)}function isValidTenant(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.name&&"string"==typeof e.slug&&isTenantStatus(e.status)}function isValidUser(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.email&&(void 0===e.role||isUserRole(e.role))}function normalizeTenantStatus(e){if("string"==typeof e){let t=e.toUpperCase();if(isTenantStatus(t))return t}return console.warn("Invalid tenant status received:",e,"defaulting to ACTIVE"),"ACTIVE"}function normalizeUserRole(e){if("string"==typeof e){let t=e.toUpperCase();if(isUserRole(t))return t}return console.warn("Invalid user role received:",e,"defaulting to STAFF"),"STAFF"}let o=(0,a.createContext)(void 0);function TenantProvider(e){let{children:t,initialTenant:n=null}=e,[l,d]=(0,a.useState)(n),[c,u]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0),[x,f]=(0,a.useState)(null),g=(0,s.useRouter)();return(0,a.useEffect)(()=>{let e=!0;async function loadTenantAndUser(){try{h(!0),f(null);let{data:{session:t},error:r}=await i.OQ.auth.getSession();if(r)throw r;if(!t){e&&(u(null),d(null),h(!1));return}let a=getTenantSlug();if(!a&&!n){e&&(f(Error("No tenant context available")),h(!1));return}let{data:s,error:l}=await i.OQ.from("users").select("\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          ").eq("id",t.user.id).single();if(l)throw l;let o=s.user_tenants.find(e=>e.tenant.slug===(a||(null==n?void 0:n.slug)));if(!o)throw Error("User does not have access to this tenant");if(e){let e={...o.tenant,status:normalizeTenantStatus(o.tenant.status)},t={id:s.id,email:s.email,tenant_id:o.tenant.id,role:normalizeUserRole(o.role),first_name:s.first_name,last_name:s.last_name,avatar_url:s.avatar_url,created_at:s.created_at,updated_at:s.updated_at};if(isValidTenant(e)&&isValidUser(t))u(t),d(e);else throw Error("Invalid tenant or user data received");h(!1)}}catch(t){console.error("Error loading tenant context:",t),e&&(f(t),h(!1))}}loadTenantAndUser();let{data:{subscription:t}}=i.OQ.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?loadTenantAndUser():"SIGNED_OUT"===e&&(u(null),d(null),g.push("/login"))});return()=>{e=!1,t.unsubscribe()}},[g,n]),(0,r.jsx)(o.Provider,{value:{tenant:l,user:c,isLoading:m,error:x},children:t})}function useTenant(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useTenant must be used within a TenantProvider");return e}function getTenantSlug(){let e=window.location.hostname,t=e.split(".")[0];if(t&&"www"!==t&&"localhost"!==t)return t;let n=window.location.pathname.match(/^\/t\/([^\/]+)/);if(n)return n[1];let r=new URLSearchParams(window.location.search),a=r.get("tenant");if(a)return a;let s=document.cookie.split("; ").find(e=>e.startsWith("tenant-slug="));return s?s.split("=")[1]:null}},7341:function(e,t,n){"use strict";n.d(t,{OQ:function(){return a}});var r=n(4243);let createClient=()=>(0,r.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo");function getSupabaseClient(){return createClient()}let a=getSupabaseClient()},4033:function(e,t,n){e.exports=n(94)}}]);