"use strict";exports.id=599,exports.ids=[599],exports.modules={661:(e,t,r)=>{r.r(t),r.d(t,{ProtectedRoute:()=>ProtectedRoute,withAuth:()=>withAuth});var s=r(784),n=r(9885),i=r(7114),o=r(2214);let a={STAFF:1,MANAGER:2,ADMIN:3,OWNER:4};function ProtectedRoute({children:e,requiredRole:t,requiredPermissions:r,fallback:a}){let{user:c,tenant:l,isLoading:d,error:u}=(0,o.useTenant)(),h=(0,i.useRouter)();return((0,n.useEffect)(()=>{if(!d&&!c){let e=window.location.pathname,t=`/login?returnUrl=${encodeURIComponent(e)}`;h.push(t)}},[c,d,h]),d)?a||s.jsx("div",{children:"Loading..."}):u?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[s.jsx("h2",{className:"text-xl font-semibold text-red-600",children:"Error"}),s.jsx("p",{className:"text-gray-600 mt-2",children:u.message}),s.jsx("button",{onClick:()=>h.push("/login"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Go to Login"})]}):c&&l?"ACTIVE"!==l.status?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[s.jsx("h2",{className:"text-xl font-semibold text-orange-600",children:"Tenant Suspended"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"This organization's account is currently suspended."}),s.jsx("button",{onClick:()=>h.push("/login"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Switch Organization"})]}):t&&!hasRequiredRole(c,t)||r&&!hasRequiredPermissions(c,r)?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[s.jsx("h2",{className:"text-xl font-semibold text-red-600",children:"Access Denied"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"You don't have the required permissions to access this page."}),s.jsx("button",{onClick:()=>h.push("/dashboard"),className:"mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600",children:"Go to Dashboard"})]}):s.jsx(s.Fragment,{children:e}):null}function hasRequiredRole(e,t){let r=a[e.role]||0,s=a[t]||0;return r>=s}function hasRequiredPermissions(e,t){let r={OWNER:["*"],ADMIN:["read","write","delete","manage_users","manage_settings"],MANAGER:["read","write","delete"],STAFF:["read","write"]}[e.role]||[];return t.every(e=>r.includes("*")||r.includes(e))}function withAuth(e,t){return function(r){return s.jsx(ProtectedRoute,{requiredRole:t?.requiredRole,requiredPermissions:t?.requiredPermissions,fallback:t?.fallback,children:s.jsx(e,{...r})})}}},9411:(e,t,r)=>{r.d(t,{i:()=>a});var s=r(5153);let n=(0,s.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/auth/ProtectedRoute.tsx`),{__esModule:i,$$typeof:o}=n;n.default;let a=(0,s.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/auth/ProtectedRoute.tsx#ProtectedRoute`);(0,s.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/auth/ProtectedRoute.tsx#withAuth`)}};