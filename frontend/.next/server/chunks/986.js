"use strict";exports.id=986,exports.ids=[986],exports.modules={7521:(e,s,t)=>{t.r(s),t.d(s,{DashboardContent:()=>DashboardContent});var r=t(784),a=t(2214);function DashboardContent(){let{tenant:e,user:s}=(0,a.useTenant)();return(0,r.jsxs)("div",{children:[r.jsx("div",{className:"md:flex md:items-center md:justify-between",children:r.jsx("div",{className:"min-w-0 flex-1",children:r.jsx("h2",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight",children:"Dashboard"})})}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[r.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:r.jsx("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),r.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[r.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Orders"}),r.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"142"})]})})]})})}),r.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:r.jsx("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),r.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[r.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Revenue"}),r.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"$12,426"})]})})]})})}),r.jsx("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:r.jsx("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),r.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[r.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Growth"}),r.jsx("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"+12.3%"})]})})]})})})]}),s&&(0,r.jsxs)("div",{className:"mt-6 bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:["Welcome back, ",s.first_name||s.email,"!"]}),r.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:e&&`Managing ${e.name}`})]})]})}},7888:(e,s,t)=>{t.d(s,{c:()=>l});var r=t(5153);let a=(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/dashboard/DashboardContent.tsx`),{__esModule:d,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/dashboard/DashboardContent.tsx#DashboardContent`)}};