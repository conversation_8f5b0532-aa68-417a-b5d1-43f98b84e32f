"use strict";exports.id=998,exports.ids=[998],exports.modules={5011:(e,t)=>{var r=Object.prototype.toString},1099:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return DraftMode}});let o=r(3657);let DraftMode=class DraftMode{get isEnabled(){return this._provider.isEnabled}enable(){if(!(0,o.staticGenerationBailout)("draftMode().enable()"))return this._provider.enable()}disable(){if(!(0,o.staticGenerationBailout)("draftMode().disable()"))return this._provider.disable()}constructor(e){this._provider=e}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{headers:function(){return headers},cookies:function(){return cookies},draftMode:function(){return draftMode}});let o=r(6888),n=r(1306),a=r(6449),s=r(4580),i=r(2934),l=r(3657),u=r(1099);function headers(){if((0,l.staticGenerationBailout)("headers",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return n.HeadersAdapter.seal(new Headers({}));let e=s.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: headers() expects to have requestAsyncStorage, none available.");return e.headers}function cookies(){if((0,l.staticGenerationBailout)("cookies",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return o.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({})));let e=s.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: cookies() expects to have requestAsyncStorage, none available.");let t=i.actionAsyncStorage.getStore();return t&&(t.isAction||t.isAppRoute)?e.mutableCookies:e.cookies}function draftMode(){let e=s.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: draftMode() expects to have requestAsyncStorage, none available.");return new u.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1306:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyHeadersError:function(){return ReadonlyHeadersError},HeadersAdapter:function(){return HeadersAdapter}});let o=r(9165);let ReadonlyHeadersError=class ReadonlyHeadersError extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ReadonlyHeadersError}};let HeadersAdapter=class HeadersAdapter extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return o.ReflectAdapter.get(t,r,n);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==s)return o.ReflectAdapter.get(t,s,n)},set(t,r,n,a){if("symbol"==typeof r)return o.ReflectAdapter.set(t,r,n,a);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return o.ReflectAdapter.set(t,i??r,n,a)},has(t,r){if("symbol"==typeof r)return o.ReflectAdapter.has(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==a&&o.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return o.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===a||o.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ReadonlyHeadersError.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new HeadersAdapter(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,o]of this.entries())e.call(t,o,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},9165:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return ReflectAdapter}});let ReflectAdapter=class ReflectAdapter{static get(e,t,r){let o=Reflect.get(e,t,r);return"function"==typeof o?o.bind(e):o}static set(e,t,r,o){return Reflect.set(e,t,r,o)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},6888:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyRequestCookiesError:function(){return ReadonlyRequestCookiesError},RequestCookiesAdapter:function(){return RequestCookiesAdapter},getModifiedCookieValues:function(){return getModifiedCookieValues},appendMutableCookies:function(){return appendMutableCookies},MutableRequestCookiesAdapter:function(){return MutableRequestCookiesAdapter}});let o=r(6449),n=r(9165);let ReadonlyRequestCookiesError=class ReadonlyRequestCookiesError extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new ReadonlyRequestCookiesError}};let RequestCookiesAdapter=class RequestCookiesAdapter{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ReadonlyRequestCookiesError.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}};let a=Symbol.for("next.mutated.cookies");function getModifiedCookieValues(e){let t=e[a];return t&&Array.isArray(t)&&0!==t.length?t:[]}function appendMutableCookies(e,t){let r=getModifiedCookieValues(t);if(0===r.length)return!1;let n=new o.ResponseCookies(e),a=n.getAll();for(let e of r)n.set(e);for(let e of a)n.set(e);return!0}let MutableRequestCookiesAdapter=class MutableRequestCookiesAdapter{static wrap(e,t){let r=new o.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],i=new Set,updateResponseCookies=()=>{var e;let n=null==fetch.__nextGetStaticStore?void 0:null==(e=fetch.__nextGetStaticStore.call(fetch))?void 0:e.getStore();n&&(n.pathWasRevalidated=!0);let a=r.getAll();if(s=a.filter(e=>i.has(e.name)),t){let e=[];for(let t of s){let r=new o.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case a:return s;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{updateResponseCookies()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{updateResponseCookies()}};default:return n.ReflectAdapter.get(e,t,r)}}})}}},4596:(e,t,r)=>{e.exports=r(2491)},7925:(e,t,r)=>{r.d(t,{lx:()=>createServerClient}),r(5011);var o=r(6163);function _isPlaceholder(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function _curry1(e){return function f1(t){return 0==arguments.length||_isPlaceholder(t)?f1:e.apply(this,arguments)}}function _curry2(e){return function f2(t,r){switch(arguments.length){case 0:return f2;case 1:return _isPlaceholder(t)?f2:_curry1(function(r){return e(t,r)});default:return _isPlaceholder(t)&&_isPlaceholder(r)?f2:_isPlaceholder(t)?_curry1(function(t){return e(t,r)}):_isPlaceholder(r)?_curry1(function(r){return e(t,r)}):e(t,r)}}}function _curry3(e){return function f3(t,r,o){switch(arguments.length){case 0:return f3;case 1:return _isPlaceholder(t)?f3:_curry2(function(r,o){return e(t,r,o)});case 2:return _isPlaceholder(t)&&_isPlaceholder(r)?f3:_isPlaceholder(t)?_curry2(function(t,o){return e(t,r,o)}):_isPlaceholder(r)?_curry2(function(r,o){return e(t,r,o)}):_curry1(function(o){return e(t,r,o)});default:return _isPlaceholder(t)&&_isPlaceholder(r)&&_isPlaceholder(o)?f3:_isPlaceholder(t)&&_isPlaceholder(r)?_curry2(function(t,r){return e(t,r,o)}):_isPlaceholder(t)&&_isPlaceholder(o)?_curry2(function(t,o){return e(t,r,o)}):_isPlaceholder(r)&&_isPlaceholder(o)?_curry2(function(r,o){return e(t,r,o)}):_isPlaceholder(t)?_curry1(function(t){return e(t,r,o)}):_isPlaceholder(r)?_curry1(function(r){return e(t,r,o)}):_isPlaceholder(o)?_curry1(function(o){return e(t,r,o)}):e(t,r,o)}}}function _isObject(e){return"[object Object]"===Object.prototype.toString.call(e)}function _has(e,t){return Object.prototype.hasOwnProperty.call(t,e)}var n=_curry3(function(e,t,r){var o,n={};for(o in r=r||{},t=t||{})_has(o,t)&&(n[o]=_has(o,r)?e(o,t[o],r[o]):t[o]);for(o in r)_has(o,r)&&!_has(o,n)&&(n[o]=r[o]);return n}),a=_curry3(function mergeDeepWithKey(e,t,r){return n(function(t,r,o){return _isObject(r)&&_isObject(o)?mergeDeepWithKey(e,r,o):e(t,r,o)},t,r)}),s=_curry2(function(e,t){return a(function(e,t,r){return r},e,t)}),i={path:"/",sameSite:"lax",httpOnly:!1,maxAge:31536e6};function createChunks(e,t,r){let o=r??3180,n=encodeURIComponent(t);if(n.length<=o)return[{name:e,value:t}];let a=[];for(;n.length>0;){let e=n.slice(0,o),t=e.lastIndexOf("%");t>o-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}a.push(r),n=n.slice(e.length)}return a.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function combineChunks(e,t){let r=await t(e);if(r)return r;let o=[];for(let r=0;;r++){let n=`${e}.${r}`,a=await t(n);if(!a)break;o.push(a)}if(o.length>0)return o.join("")}async function deleteChunks(e,t,r){let o=await t(e);if(o){await r(e);return}for(let o=0;;o++){let n=`${e}.${o}`,a=await t(n);if(!a)break;await r(n)}}function createServerClient(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{cookies:n,cookieOptions:a,...l}=r;(null==a?void 0:a.name)&&(l.auth={...l.auth,storageKey:a.name});let u=s({global:{headers:{"X-Client-Info":"supabase-ssr/0.1.0"}},auth:{flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:{isServer:!0,getItem:async e=>{let t=await combineChunks(e,async e=>{if("function"==typeof n.get)return await n.get(e)});return t},setItem:async(e,t)=>{let r=createChunks(e,t);await Promise.all(r.map(async e=>{"function"==typeof n.set&&await n.set(e.name,e.value,{...i,...a,maxAge:i.maxAge})}))},removeItem:async e=>{if("function"==typeof n.remove&&"function"!=typeof n.get){console.log("Removing chunked cookie without a `get` method is not supported.\n\n	When you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\n\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client");return}deleteChunks(e,async e=>{if("function"==typeof n.get)return await n.get(e)},async e=>{if("function"==typeof n.remove)return await n.remove(e,{...i,...a,maxAge:0})})}}}},l);return(0,o.eI)(e,t,u)}}};