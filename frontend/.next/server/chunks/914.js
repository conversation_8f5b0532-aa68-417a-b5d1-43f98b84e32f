exports.id=914,exports.ids=[914],exports.modules={2213:e=>{function webpackEmptyContext(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id=2213,e.exports=webpackEmptyContext},8359:()=>{},3739:()=>{},8692:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,3724,23)),Promise.resolve().then(n.t.bind(n,5365,23)),Promise.resolve().then(n.t.bind(n,4900,23)),Promise.resolve().then(n.t.bind(n,4714,23)),Promise.resolve().then(n.t.bind(n,5392,23)),Promise.resolve().then(n.t.bind(n,8898,23))},5752:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,614,23))},8838:(e,t,n)=>{Promise.resolve().then(n.bind(n,2214))},2214:(e,t,n)=>{"use strict";n.r(t),n.d(t,{TenantProvider:()=>TenantProvider,useTenant:()=>useTenant});var r=n(784),s=n(9885),a=n(7114),o=n(8926);let i=["ACTIVE","SUSPENDED","INACTIVE","PENDING"],l=["OWNER","ADMIN","MANAGER","STAFF","READONLY"];function isTenantStatus(e){return"string"==typeof e&&i.includes(e)}function isUserRole(e){return"string"==typeof e&&l.includes(e)}function isValidTenant(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.name&&"string"==typeof e.slug&&isTenantStatus(e.status)}function isValidUser(e){return!!e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.email&&(void 0===e.role||isUserRole(e.role))}function normalizeTenantStatus(e){if("string"==typeof e){let t=e.toUpperCase();if(isTenantStatus(t))return t}return console.warn("Invalid tenant status received:",e,"defaulting to ACTIVE"),"ACTIVE"}function normalizeUserRole(e){if("string"==typeof e){let t=e.toUpperCase();if(isUserRole(t))return t}return console.warn("Invalid user role received:",e,"defaulting to STAFF"),"STAFF"}let d=(0,s.createContext)(void 0);function TenantProvider({children:e,initialTenant:t=null}){let[n,i]=(0,s.useState)(t),[l,u]=(0,s.useState)(null),[c,m]=(0,s.useState)(!0),[f,x]=(0,s.useState)(null),h=(0,a.useRouter)();return(0,s.useEffect)(()=>{let e=!0;async function loadTenantAndUser(){try{m(!0),x(null);let{data:{session:n},error:r}=await o.OQ.auth.getSession();if(r)throw r;if(!n){e&&(u(null),i(null),m(!1));return}let s=getTenantSlug();if(!s&&!t){e&&(x(Error("No tenant context available")),m(!1));return}let{data:a,error:l}=await o.OQ.from("users").select(`
            *,
            user_tenants!inner(
              role,
              tenant:tenants(*)
            )
          `).eq("id",n.user.id).single();if(l)throw l;let d=a.user_tenants.find(e=>e.tenant.slug===(s||t?.slug));if(!d)throw Error("User does not have access to this tenant");if(e){let e={...d.tenant,status:normalizeTenantStatus(d.tenant.status)},t={id:a.id,email:a.email,tenant_id:d.tenant.id,role:normalizeUserRole(d.role),first_name:a.first_name,last_name:a.last_name,avatar_url:a.avatar_url,created_at:a.created_at,updated_at:a.updated_at};if(isValidTenant(e)&&isValidUser(t))u(t),i(e);else throw Error("Invalid tenant or user data received");m(!1)}}catch(t){console.error("Error loading tenant context:",t),e&&(x(t),m(!1))}}loadTenantAndUser();let{data:{subscription:n}}=o.OQ.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?loadTenantAndUser():"SIGNED_OUT"===e&&(u(null),i(null),h.push("/login"))});return()=>{e=!1,n.unsubscribe()}},[h,t]),r.jsx(d.Provider,{value:{tenant:n,user:l,isLoading:c,error:f},children:e})}function useTenant(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useTenant must be used within a TenantProvider");return e}function getTenantSlug(){return null}},8926:(e,t,n)=>{"use strict";n.d(t,{OQ:()=>s});var r=n(5903);let s=(0,r.AY)("https://cgzcndxnfldupgdddnra.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo")},9113:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>RootLayout});var r=n(4656),s=n(5579);function RootLayout({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{children:r.jsx(s.z,{children:e})})})}n(5023)},5666:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>NotFound});var r=n(4656),s=n(4353),a=n.n(s);function NotFound(){return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-6xl font-bold text-gray-900 dark:text-white mb-2",children:"404"}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4",children:"Page Not Found"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:"The page you're looking for doesn't exist or the tenant is not active."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(a(),{href:"/",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Go to Home"}),r.jsx("div",{className:"text-center",children:r.jsx(a(),{href:"/login",className:"text-primary-600 hover:text-primary-500 text-sm font-medium",children:"Sign in to your account"})})]})]})})}},5579:(e,t,n)=>{"use strict";n.d(t,{z:()=>i});var r=n(5153);let s=(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx`),{__esModule:a,$$typeof:o}=s;s.default;let i=(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#TenantProvider`);(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#useTenant`)},5023:()=>{}};