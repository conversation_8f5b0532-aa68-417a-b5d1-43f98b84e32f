"use strict";exports.id=647,exports.ids=[647],exports.modules={647:(e,t,r)=>{r.d(t,{a:()=>useAuth});var s=r(9885),a=r(7114),n=r(8926),i=r(2214);function useAuth(){let[e,t]=(0,s.useState)(!1),[r,o]=(0,s.useState)(null),l=(0,a.useRouter)(),{tenant:u,user:c}=(0,i.useTenant)(),signIn=async e=>{try{t(!0),o(null);let{data:r,error:s}=await n.OQ.auth.signInWithPassword({email:e.email,password:e.password});if(s)throw s;if(e.tenant_slug){let t=await verifyTenantAccess(r.user.id,e.tenant_slug);if(!t)throw Error("Access denied to this tenant")}let a=new URLSearchParams(window.location.search).get("returnUrl")||"/dashboard";return l.push(a),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signUp=async e=>{try{t(!0),o(null);let{data:r,error:s}=await n.OQ.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.first_name,last_name:e.last_name}}});if(s)throw s;return r.user&&!e.tenant_slug&&await createTenant(r.user.id,e),{user:r.user,session:r.session}}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},signOut=async()=>{try{t(!0);let{error:e}=await n.OQ.auth.signOut();if(e)throw e;l.push("/login")}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},resetPassword=async e=>{try{t(!0),o(null);let{error:r}=await n.OQ.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}},updatePassword=async e=>{try{t(!0),o(null);let{error:r}=await n.OQ.auth.updateUser({password:e});if(r)throw r;return!0}catch(t){let e=t instanceof Error?t.message:"An error occurred";throw o(e),t}finally{t(!1)}};return{user:c,tenant:u,loading:e,error:r,signIn,signUp,signOut,resetPassword,updatePassword}}async function verifyTenantAccess(e,t){try{let{data:r,error:s}=await n.OQ.from("user_tenants").select("tenant_id, tenants!inner(slug, status)").eq("user_id",e).eq("tenants.slug",t).eq("tenants.status","ACTIVE").single();return!s&&!!r}catch{return!1}}async function createTenant(e,t){try{let r=t.tenant_name.toLowerCase().replace(/[^a-z0-9]/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,""),{data:s,error:a}=await n.OQ.from("tenants").insert({name:t.tenant_name,slug:r,email:t.email,status:"ACTIVE"}).select().single();if(a)throw a;let{error:i}=await n.OQ.from("users").insert({id:e,email:t.email,first_name:t.first_name,last_name:t.last_name});if(i)throw i;let{error:o}=await n.OQ.from("user_tenants").insert({user_id:e,tenant_id:s.id,role:"OWNER"});if(o)throw o;return s}catch(e){throw console.error("Error creating tenant:",e),e}}}};