"use strict";exports.id=657,exports.ids=[657],exports.modules={9195:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_ERROR_CODE:function(){return r},DynamicServerError:function(){return DynamicServerError}});let r="DYNAMIC_SERVER_USAGE";let DynamicServerError=class DynamicServerError extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=r}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2236:(e,t,r)=>{function maybePostpone(e,t){if(!e.isStaticGeneration||!e.experimental.ppr)return;let o=r(3542);"function"==typeof o.unstable_postpone&&o.unstable_postpone(t)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"maybePostpone",{enumerable:!0,get:function(){return maybePostpone}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3657:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"staticGenerationBailout",{enumerable:!0,get:function(){return staticGenerationBailout}});let o=r(9195),n=r(2236),a=r(5869);let StaticGenBailoutError=class StaticGenBailoutError extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}};function formatErrorMessage(e,t){let{dynamic:r,link:o}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(o?" See more info here: "+o:"")}let staticGenerationBailout=(e,t)=>{let r=a.staticGenerationAsyncStorage.getStore();if(!r)return!1;if(r.forceStatic)return!0;if(r.dynamicShouldError){var i;throw new StaticGenBailoutError(formatErrorMessage(e,{...t,dynamic:null!=(i=null==t?void 0:t.dynamic)?i:"error"}))}let u=formatErrorMessage(e,{...t,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if((0,n.maybePostpone)(r,u),r.revalidate=0,(null==t?void 0:t.dynamic)||(r.staticPrefetchBailout=!0),r.isStaticGeneration){let t=new o.DynamicServerError(u);throw r.dynamicUsageDescription=e,r.dynamicUsageStack=t.stack,t}return!1};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6132:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},7096:(e,t,r)=>{e.exports=r(399)},3542:(e,t,r)=>{e.exports=r(7096).vendored["react-rsc"].React}};