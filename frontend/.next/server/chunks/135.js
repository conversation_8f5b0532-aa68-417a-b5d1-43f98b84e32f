exports.id=135,exports.ids=[135],exports.modules={9416:(e,r,a)=>{Promise.resolve().then(a.bind(a,661)),Promise.resolve().then(a.bind(a,5577)),Promise.resolve().then(a.bind(a,2214))},5577:(e,r,a)=>{"use strict";a.r(r),a.d(r,{DashboardLayout:()=>DashboardLayout});var t=a(784),s=a(9885),n=a(7114),i=a(2451),o=a.n(i),l=a(1440),d=a.n(l),c=a(647);function TopNavigation({onMenuClick:e,darkMode:r,onDarkModeToggle:a,tenant:n}){let[i,l]=(0,s.useState)(!1),{user:m,signOut:h}=(0,c.a)(),x=(0,s.useRef)(null);(0,s.useEffect)(()=>{let handleClickOutside=e=>{x.current&&!x.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",handleClickOutside),()=>document.removeEventListener("mousedown",handleClickOutside)},[]);let handleLogout=async()=>{try{await h()}catch(e){console.error("Logout error:",e)}};return t.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",role:"banner",children:(0,t.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 lg:px-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("button",{type:"button",className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 lg:hidden",onClick:e,"aria-label":"Open sidebar","aria-expanded":"false",children:t.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,t.jsxs)("div",{className:"flex items-center ml-2 lg:hidden",children:[(0,t.jsxs)(d(),{href:"/",className:"flex items-center",children:[t.jsx(o(),{src:"/logo.svg",alt:"TapDine",width:32,height:32,className:"h-8 w-8"}),t.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"TapDine"})]}),n&&t.jsx("div",{className:"ml-4 pl-4 border-l border-gray-300 dark:border-gray-600",children:t.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:n.name})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("button",{type:"button",onClick:a,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-gray-300 dark:hover:text-gray-200","aria-label":r?"Switch to light mode":"Switch to dark mode",children:r?t.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}):t.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})}),(0,t.jsxs)("div",{className:"relative",ref:x,children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center max-w-xs bg-gray-100 dark:bg-gray-700 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800",onClick:()=>l(!i),"aria-label":"User menu","aria-expanded":i,"aria-haspopup":"true",children:[t.jsx("span",{className:"sr-only",children:"Open user menu"}),m?.avatar_url?t.jsx(o(),{className:"h-8 w-8 rounded-full",src:m.avatar_url,alt:`${m.first_name} ${m.last_name}`,width:32,height:32}):t.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center",children:t.jsx("span",{className:"text-sm font-medium text-white",children:m?.first_name&&m?.last_name?`${m.first_name.charAt(0)}${m.last_name.charAt(0)}`:m?.email?.charAt(0).toUpperCase()||"U"})})]}),i&&t.jsx("div",{className:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"font-medium truncate",children:[m?.first_name," ",m?.last_name]}),t.jsx("div",{className:"text-gray-500 dark:text-gray-400 truncate",children:m?.email})]}),t.jsx(d(),{href:"/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>l(!1),children:"Your Profile"}),t.jsx(d(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:()=>l(!1),children:"Settings"}),t.jsx("button",{type:"button",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",role:"menuitem",onClick:handleLogout,children:"Sign out"})]})})]})]})]})})}let m=[{name:"Dashboard",href:"/dashboard",icon:(0,t.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v14l-5-3-5 3V5z"})]})},{name:"Menu",href:"/menu",icon:t.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},{name:"Orders",href:"/orders",icon:t.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{name:"Staff",href:"/staff",icon:t.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})},{name:"Settings",href:"/settings",icon:(0,t.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}];function Sidebar({isOpen:e,onClose:r,tenant:a}){let s=(0,n.usePathname)(),getTenantHref=e=>a?.slug?`/t/${a.slug}${e}`:e,isActiveRoute=e=>{let r=getTenantHref(e);return s===r||s.startsWith(r+"/")};return t.jsx(t.Fragment,{children:t.jsx("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${e?"translate-x-0":"-translate-x-full"}`,children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(o(),{src:"/logo.svg",alt:"TapDine",width:32,height:32,className:"h-8 w-8"}),t.jsx("span",{className:"ml-2 text-xl font-bold text-gray-900 dark:text-white",children:"TapDine"})]}),t.jsx("button",{type:"button",className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 lg:hidden",onClick:r,"aria-label":"Close sidebar",children:t.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),t.jsx("nav",{className:"flex-1 px-2 py-4 space-y-1 overflow-y-auto",role:"navigation","aria-label":"Main navigation",children:m.map(e=>{let a=isActiveRoute(e.href);return(0,t.jsxs)(d(),{href:getTenantHref(e.href),className:`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${a?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"}`,onClick:r,"aria-current":a?"page":void 0,children:[t.jsx("span",{className:`mr-3 flex-shrink-0 transition-colors duration-200 ${a?"text-primary-500 dark:text-primary-300":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"}`,children:e.icon}),e.name]},e.name)})}),a&&t.jsx("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"flex-shrink-0",children:a.logo_url?t.jsx(o(),{src:a.logo_url,alt:a.name,width:32,height:32,className:"h-8 w-8 rounded-full"}):t.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center",children:t.jsx("span",{className:"text-sm font-medium text-primary-600 dark:text-primary-300",children:a.name.charAt(0).toUpperCase()})})}),(0,t.jsxs)("div",{className:"ml-3 min-w-0",children:[t.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:a.name}),t.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:a.slug})]})]})})]})})})}function MobileOverlay({isOpen:e,onClose:r}){return e?t.jsx("div",{className:"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity duration-300 ease-linear lg:hidden",onClick:r,"aria-hidden":"true"}):null}var h=a(2214);function DashboardLayout({children:e}){let[r,a]=(0,s.useState)(!1),[i,o]=(0,s.useState)(!1),l=(0,n.usePathname)(),{tenant:d}=(0,h.useTenant)();return(0,s.useEffect)(()=>{let e="true"===localStorage.getItem("darkMode");o(e),e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[]),(0,s.useEffect)(()=>{a(!1)},[l]),(0,t.jsxs)("div",{className:"flex h-screen bg-gray-50 dark:bg-gray-900",children:[t.jsx(MobileOverlay,{isOpen:r,onClose:()=>{a(!1)}}),t.jsx(Sidebar,{isOpen:r,onClose:()=>a(!1),tenant:d}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden lg:ml-64",children:[t.jsx(TopNavigation,{onMenuClick:()=>a(!0),darkMode:i,onDarkModeToggle:()=>{let e=!i;o(e),localStorage.setItem("darkMode",e.toString()),e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},tenant:d}),t.jsx("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 lg:p-6",role:"main","aria-label":"Main content",children:t.jsx("div",{className:"max-w-7xl mx-auto",children:e})})]})]})}},3816:(e,r,a)=>{"use strict";a.r(r),a.d(r,{PageHeader:()=>PageHeader});var t=a(784);function PageHeader({title:e,subtitle:r,actions:a,breadcrumbs:s}){return(0,t.jsxs)("div",{className:"mb-6",children:[s&&s.length>0&&t.jsx("nav",{className:"mb-4","aria-label":"Breadcrumb",children:t.jsx("ol",{className:"flex items-center space-x-2 text-sm",children:s.map((e,r)=>(0,t.jsxs)("li",{className:"flex items-center",children:[r>0&&t.jsx("svg",{className:"w-4 h-4 mx-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),e.href?t.jsx("a",{href:e.href,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:e.label}):t.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:e.label})]},r))})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[t.jsx("h1",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight",children:e}),r&&t.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]}),a&&t.jsx("div",{className:"mt-4 flex-shrink-0 sm:mt-0 sm:ml-4",children:a})]})]})}a(9885)},3770:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>TenantLayout,generateMetadata:()=>generateMetadata});var t=a(4656),s=a(4047),n=a(5579),i=a(9394),o=a(4596),l=a(5153);let d=(0,l.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/layout/DashboardLayout.tsx`),{__esModule:c,$$typeof:m}=d;d.default;let h=(0,l.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/layout/DashboardLayout.tsx#DashboardLayout`);var x=a(9411);async function generateMetadata({params:e}){let r=await (0,o.cookies)(),a=(0,i.eI)(r);try{let{data:r}=await a.from("tenants").select("name, description").eq("slug",e.tenant).eq("status","ACTIVE").single();if(!r)return{title:"Tenant Not Found - TapDine",description:"The requested restaurant could not be found."};return{title:`${r.name} - TapDine Dashboard`,description:r.description||`${r.name} restaurant management dashboard powered by TapDine`,robots:{index:!1,follow:!1}}}catch(e){return console.error("Error generating metadata:",e),{title:"Error - TapDine",description:"An error occurred while loading the restaurant dashboard."}}}async function TenantLayout({children:e,params:r}){let a=await (0,o.cookies)(),l=(0,i.eI)(a);try{let{data:a,error:i}=await l.from("tenants").select("*").eq("slug",r.tenant).eq("status","ACTIVE").single();return(i||!a)&&(console.error("Tenant not found:",i),(0,s.notFound)()),t.jsx(n.z,{initialTenant:a,children:t.jsx(x.i,{children:t.jsx(h,{children:e})})})}catch(e){console.error("Error in tenant layout:",e),(0,s.notFound)()}}},2816:(e,r,a)=>{"use strict";a.d(r,{m:()=>o});var t=a(5153);let s=(0,t.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/layout/PageHeader.tsx`),{__esModule:n,$$typeof:i}=s;s.default;let o=(0,t.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/components/layout/PageHeader.tsx#PageHeader`)}};