(()=>{var e={};e.id=834,e.ids=[834],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},2819:(e,t,r)=>{"use strict";r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>v,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{GET:()=>GET,dynamic:()=>l,runtime:()=>n});var a=r(884),i=r(6132),o=r(5798),u=r(9394);let n="nodejs",l="force-dynamic";async function GET(e){try{let t=e.nextUrl.searchParams.get("slug");if(!t)return o.Z.json({error:"Slug parameter is required"},{status:400});if(!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(t)||t.length<3||t.length>50)return o.Z.json({available:!1,error:"Invalid slug format"},{status:400});if(["api","admin","app","www","mail","ftp","localhost","dashboard","billing","support","help","docs","blog","status","staging","dev","test","demo","cdn","assets","static","public","private","secure","internal","system","root","user","account","login","signup","auth","oauth","settings","profile","home","index","about","contact","terms","privacy","legal","pricing","features","enterprise"].includes(t.toLowerCase()))return o.Z.json({available:!1,error:"Slug is reserved"},{status:400});let r=(0,u.Ic)(),{data:s,error:a}=await r.from("tenants").select("slug").eq("slug",t).single();if(a&&"PGRST116"!==a.code)return console.error("Database error:",a),o.Z.json({error:"Database error"},{status:500});return o.Z.json({available:!s,slug:t})}catch(e){return console.error("Slug validation error:",e),o.Z.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/validate-slug/route",pathname:"/api/auth/validate-slug",filename:"route",bundlePath:"app/api/auth/validate-slug/route"},resolvedPagePath:"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/api/auth/validate-slug/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:g,headerHooks:x,staticGenerationBailout:m}=c,v="/api/auth/validate-slug/route"}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[657,945,998,107,394],()=>__webpack_exec__(2819));module.exports=r})();