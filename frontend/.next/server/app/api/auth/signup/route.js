/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/signup/route";
exports.ids = ["app/api/auth/signup/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_signup_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/auth/signup/route.ts */ \"(rsc)/./src/app/api/auth/signup/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/signup/route\",\n        pathname: \"/api/auth/signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/signup/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/api/auth/signup/route.ts\",\n    nextConfigOutput,\n    userland: _Users_shiva_Desktop_BHEEMDINE_frontend_src_app_api_auth_signup_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/signup/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/signup/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/auth/signup/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * API route for tenant admin signup\n * Creates tenant, admin user, and returns session\n */ \n\n\nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nasync function POST(request) {\n    try {\n        console.log(\"Starting signup process...\");\n        const body = await request.json();\n        console.log(\"Received body:\", {\n            ...body,\n            password: \"[REDACTED]\"\n        });\n        const { businessName, tenantSlug, adminEmail, password } = body;\n        // Basic validation\n        if (!businessName || !tenantSlug || !adminEmail || !password) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"All fields are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Create Supabase service client\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseServiceKey);\n        console.log(\"Checking if tenant slug exists...\");\n        // Check if slug is already taken\n        const { data: existingTenant } = await supabase.from(\"tenants\").select(\"slug\").eq(\"slug\", tenantSlug).single();\n        if (existingTenant) {\n            console.log(\"Tenant slug already exists\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Tenant slug already exists\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"Creating auth user...\");\n        // Create Supabase auth user\n        const { data: authData, error: authError } = await supabase.auth.admin.createUser({\n            email: adminEmail,\n            password: password,\n            email_confirm: true\n        });\n        if (authError || !authData.user) {\n            console.error(\"Auth user creation error:\", authError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create user account: \" + authError?.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"Auth user created:\", authData.user.id);\n        console.log(\"Creating tenant...\");\n        // Create tenant with UUID\n        const { data: tenant, error: tenantError } = await supabase.from(\"tenants\").insert({\n            id: crypto__WEBPACK_IMPORTED_MODULE_1___default().randomUUID(),\n            name: businessName,\n            slug: tenantSlug,\n            email: adminEmail,\n            status: \"ACTIVE\",\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (tenantError || !tenant) {\n            console.error(\"Tenant creation error:\", tenantError);\n            // Clean up auth user if tenant creation fails\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create tenant: \" + tenantError?.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"Tenant created:\", tenant.id);\n        console.log(\"Creating user profile...\");\n        // Create user profile\n        const { data: user, error: userError } = await supabase.from(\"users\").insert({\n            id: authData.user.id,\n            email: adminEmail,\n            first_name: businessName,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (userError || !user) {\n            console.error(\"User profile creation error:\", userError);\n            // Clean up auth user and tenant\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            await supabase.from(\"tenants\").delete().eq(\"id\", tenant.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create user profile: \" + userError?.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"User profile created:\", user.id);\n        console.log(\"Creating user-tenant association...\");\n        // Create user-tenant association\n        const { error: associationError } = await supabase.from(\"user_tenants\").insert({\n            id: crypto__WEBPACK_IMPORTED_MODULE_1___default().randomUUID(),\n            user_id: authData.user.id,\n            tenant_id: tenant.id,\n            role: \"OWNER\",\n            is_active: true\n        });\n        if (associationError) {\n            console.error(\"User-tenant association error:\", associationError);\n            // Clean up created records\n            await supabase.auth.admin.deleteUser(authData.user.id);\n            await supabase.from(\"tenants\").delete().eq(\"id\", tenant.id);\n            await supabase.from(\"users\").delete().eq(\"id\", user.id);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create account association: \" + associationError?.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"User-tenant association created\");\n        // Return success response\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Account created successfully\",\n            data: {\n                tenant: {\n                    id: tenant.id,\n                    name: tenant.name,\n                    slug: tenant.slug\n                },\n                user: {\n                    id: user.id,\n                    email: user.email\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Signup error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error: \" + error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/signup/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fsignup%2Froute&page=%2Fapi%2Fauth%2Fsignup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsignup%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();