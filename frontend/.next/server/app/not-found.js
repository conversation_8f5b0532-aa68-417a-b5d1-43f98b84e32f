/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXBkaW5lLWZyb250ZW5kLz9kYzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TenantContext.tsx */ \"(ssr)/./src/contexts/TenantContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZmcm9udGVuZCUyRnNyYyUyRmNvbnRleHRzJTJGVGVuYW50Q29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8/NTdiYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9mcm9udGVuZC9zcmMvY29udGV4dHMvVGVuYW50Q29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fcontexts%2FTenantContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_type_guards__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/type-guards */ \"(ssr)/./src/lib/type-guards.ts\");\n/**\n * Tenant context provider for multi-tenant authentication\n */ /* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction TenantProvider({ children, initialTenant = null }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTenant);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        async function loadTenantAndUser() {\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Get current session\n                const { data: { session }, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                if (sessionError) throw sessionError;\n                if (!session) {\n                    if (mounted) {\n                        setUser(null);\n                        setTenant(null);\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Get tenant slug from various sources\n                const tenantSlug = getTenantSlug();\n                if (!tenantSlug && !initialTenant) {\n                    // No tenant context available\n                    if (mounted) {\n                        setError(new Error(\"No tenant context available\"));\n                        setIsLoading(false);\n                    }\n                    return;\n                }\n                // Fetch user's tenant data\n                const { data: userData, error: userError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"users\").select(`\n            *,\n            user_tenants!inner(\n              role,\n              tenant:tenants(*)\n            )\n          `).eq(\"id\", session.user.id).single();\n                if (userError) throw userError;\n                // Find the matching tenant\n                const userTenant = userData.user_tenants.find((ut)=>ut.tenant.slug === (tenantSlug || initialTenant?.slug));\n                if (!userTenant) {\n                    throw new Error(\"User does not have access to this tenant\");\n                }\n                if (mounted) {\n                    // Validate and normalize tenant data\n                    const tenantData = {\n                        ...userTenant.tenant,\n                        status: (0,_lib_type_guards__WEBPACK_IMPORTED_MODULE_4__.normalizeTenantStatus)(userTenant.tenant.status)\n                    };\n                    // Validate and normalize user data\n                    const userDataNormalized = {\n                        id: userData.id,\n                        email: userData.email,\n                        tenant_id: userTenant.tenant.id,\n                        role: (0,_lib_type_guards__WEBPACK_IMPORTED_MODULE_4__.normalizeUserRole)(userTenant.role),\n                        first_name: userData.first_name,\n                        last_name: userData.last_name,\n                        avatar_url: userData.avatar_url,\n                        created_at: userData.created_at,\n                        updated_at: userData.updated_at\n                    };\n                    // Validate data before setting state\n                    if ((0,_lib_type_guards__WEBPACK_IMPORTED_MODULE_4__.isValidTenant)(tenantData) && (0,_lib_type_guards__WEBPACK_IMPORTED_MODULE_4__.isValidUser)(userDataNormalized)) {\n                        setUser(userDataNormalized);\n                        setTenant(tenantData);\n                    } else {\n                        throw new Error(\"Invalid tenant or user data received\");\n                    }\n                    setIsLoading(false);\n                }\n            } catch (err) {\n                console.error(\"Error loading tenant context:\", err);\n                if (mounted) {\n                    setError(err);\n                    setIsLoading(false);\n                }\n            }\n        }\n        loadTenantAndUser();\n        // Subscribe to auth changes\n        const { data: { subscription } } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            if (event === \"SIGNED_IN\" || event === \"TOKEN_REFRESHED\") {\n                loadTenantAndUser();\n            } else if (event === \"SIGNED_OUT\") {\n                setUser(null);\n                setTenant(null);\n                router.push(\"/login\");\n            }\n        });\n        return ()=>{\n            mounted = false;\n            subscription.unsubscribe();\n        };\n    }, [\n        router,\n        initialTenant\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            user,\n            isLoading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Hook to use tenant context\n */ function useTenant() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (context === undefined) {\n        throw new Error(\"useTenant must be used within a TenantProvider\");\n    }\n    return context;\n}\n/**\n * Get tenant slug from various sources\n */ function getTenantSlug() {\n    if (true) return null;\n    // 1. Check subdomain\n    const hostname = window.location.hostname;\n    const subdomain = hostname.split(\".\")[0];\n    if (subdomain && subdomain !== \"www\" && subdomain !== \"localhost\") {\n        return subdomain;\n    }\n    // 2. Check URL path\n    const pathMatch = window.location.pathname.match(/^\\/t\\/([^\\/]+)/);\n    if (pathMatch) {\n        return pathMatch[1];\n    }\n    // 3. Check query parameter\n    const params = new URLSearchParams(window.location.search);\n    const tenantParam = params.get(\"tenant\");\n    if (tenantParam) {\n        return tenantParam;\n    }\n    // 4. Check cookie\n    const tenantCookie = document.cookie.split(\"; \").find((row)=>row.startsWith(\"tenant-slug=\"));\n    if (tenantCookie) {\n        return tenantCookie.split(\"=\")[1];\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   getTenantSupabaseClient: () => (/* binding */ getTenantSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/**\n * Supabase client configuration with multi-tenant support\n */ \nconst supabaseUrl = \"https://cgzcndxnfldupgdddnra.supabase.co\";\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo\";\n// Verify configuration (only in development)\nif (false) {}\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseKey);\n// Legacy function for backward compatibility\nfunction getSupabaseClient() {\n    return createClient();\n}\n// Helper to get tenant-scoped Supabase client\nfunction getTenantSupabaseClient(tenantId) {\n    const client = getSupabaseClient();\n    // Add tenant context to all queries\n    client.auth.onAuthStateChange((event, session)=>{\n        if (session) {\n            // Set tenant context in headers for RLS\n            // Note: This is a workaround for tenant isolation\n            // RLS policies should handle tenant filtering instead\n            localStorage.setItem(\"x-tenant-id\", tenantId);\n        }\n    });\n    return client;\n}\n// Export the default client\nconst supabase = getSupabaseClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/type-guards.ts":
/*!********************************!*\
  !*** ./src/lib/type-guards.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VALID_TENANT_STATUSES: () => (/* binding */ VALID_TENANT_STATUSES),\n/* harmony export */   VALID_USER_ROLES: () => (/* binding */ VALID_USER_ROLES),\n/* harmony export */   isTenantStatus: () => (/* binding */ isTenantStatus),\n/* harmony export */   isUserRole: () => (/* binding */ isUserRole),\n/* harmony export */   isValidTenant: () => (/* binding */ isValidTenant),\n/* harmony export */   isValidUser: () => (/* binding */ isValidUser),\n/* harmony export */   normalizeTenantStatus: () => (/* binding */ normalizeTenantStatus),\n/* harmony export */   normalizeUserRole: () => (/* binding */ normalizeUserRole),\n/* harmony export */   safeConvertTenantStatus: () => (/* binding */ safeConvertTenantStatus),\n/* harmony export */   safeConvertUserRole: () => (/* binding */ safeConvertUserRole)\n/* harmony export */ });\n/**\n * Type guards for runtime validation of critical types\n */ // Enum validation constants\nconst VALID_TENANT_STATUSES = [\n    \"ACTIVE\",\n    \"SUSPENDED\",\n    \"INACTIVE\",\n    \"PENDING\"\n];\nconst VALID_USER_ROLES = [\n    \"OWNER\",\n    \"ADMIN\",\n    \"MANAGER\",\n    \"STAFF\",\n    \"READONLY\"\n];\n/**\n * Type guard for TenantStatus\n */ function isTenantStatus(value) {\n    return typeof value === \"string\" && VALID_TENANT_STATUSES.includes(value);\n}\n/**\n * Type guard for UserRole\n */ function isUserRole(value) {\n    return typeof value === \"string\" && VALID_USER_ROLES.includes(value);\n}\n/**\n * Type guard for valid tenant data\n */ function isValidTenant(tenant) {\n    if (!tenant || typeof tenant !== \"object\") return false;\n    const t = tenant;\n    return typeof t.id === \"string\" && typeof t.name === \"string\" && typeof t.slug === \"string\" && isTenantStatus(t.status);\n}\n/**\n * Type guard for valid user data\n */ function isValidUser(user) {\n    if (!user || typeof user !== \"object\") return false;\n    const u = user;\n    return typeof u.id === \"string\" && typeof u.email === \"string\" && (u.role === undefined || isUserRole(u.role));\n}\n/**\n * Validates and normalizes enum values from database\n */ function normalizeTenantStatus(value) {\n    if (typeof value === \"string\") {\n        const normalized = value.toUpperCase();\n        if (isTenantStatus(normalized)) {\n            return normalized;\n        }\n    }\n    console.warn(\"Invalid tenant status received:\", value, \"defaulting to ACTIVE\");\n    return \"ACTIVE\";\n}\n/**\n * Validates and normalizes user role from database\n */ function normalizeUserRole(value) {\n    if (typeof value === \"string\") {\n        const normalized = value.toUpperCase();\n        if (isUserRole(normalized)) {\n            return normalized;\n        }\n    }\n    console.warn(\"Invalid user role received:\", value, \"defaulting to STAFF\");\n    return \"STAFF\";\n}\n/**\n * Safe enum converter that handles both uppercase and lowercase inputs\n */ function safeConvertTenantStatus(value) {\n    const upperValue = value.toUpperCase();\n    // Handle common conversions\n    switch(upperValue){\n        case \"ACTIVE\":\n            return \"ACTIVE\";\n        case \"SUSPENDED\":\n            return \"SUSPENDED\";\n        case \"INACTIVE\":\n            return \"INACTIVE\";\n        case \"PENDING\":\n            return \"PENDING\";\n        default:\n            console.warn(`Unknown tenant status: ${value}, defaulting to ACTIVE`);\n            return \"ACTIVE\";\n    }\n}\n/**\n * Safe enum converter for user roles\n */ function safeConvertUserRole(value) {\n    const upperValue = value.toUpperCase();\n    // Handle common conversions\n    switch(upperValue){\n        case \"OWNER\":\n            return \"OWNER\";\n        case \"ADMIN\":\n            return \"ADMIN\";\n        case \"MANAGER\":\n            return \"MANAGER\";\n        case \"STAFF\":\n            return \"STAFF\";\n        case \"READONLY\":\n            return \"READONLY\";\n        default:\n            console.warn(`Unknown user role: ${value}, defaulting to STAFF`);\n            return \"STAFF\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/type-guards.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37cd149362fb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGJhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3Y2QxNDkzNjJmYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(rsc)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/**\n * Root layout with multi-tenant authentication\n */ \n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_1__.TenantProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7Q0FFQztBQUV5RDtBQUNuQztBQUVSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ0wsbUVBQWNBOzBCQUNaRTs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFwZGluZS1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUm9vdCBsYXlvdXQgd2l0aCBtdWx0aS10ZW5hbnQgYXV0aGVudGljYXRpb25cbiAqL1xuXG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFRlbmFudFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59Il0sIm5hbWVzIjpbIlRlbmFudFByb3ZpZGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * 404 Not Found page\n */ \n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-6xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                            children: \"The page you're looking for doesn't exist or the tenant is not active.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                            children: \"Go to Home\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/login\",\n                                className: \"text-primary-600 hover:text-primary-500 text-sm font-medium\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ e0),\n/* harmony export */   useTenant: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#TenantProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/contexts/TenantContext.tsx#useTenant`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/TenantContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/ramda","vendor-chunks/cookie","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();