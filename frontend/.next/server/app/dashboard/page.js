(()=>{var e={};e.id=702,e.ids=[702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},681:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>l,originalPathname:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>u});var s=t(7096),o=t(6132),i=t(7284),a=t.n(i),n=t(2564),p={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);t.d(r,p);let u=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9228)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],d=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/dashboard/page.tsx"],c="/dashboard/page",l={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6118:(e,r,t)=>{Promise.resolve().then(t.bind(t,661)),Promise.resolve().then(t.bind(t,7521))},9228:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>DashboardPage});var s=t(4656),o=t(9411),i=t(7888);function DashboardPage(){return s.jsx(o.i,{children:s.jsx(i.c,{})})}}};var r=require("../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[657,663,914,599,986],()=>__webpack_exec__(681));module.exports=t})();