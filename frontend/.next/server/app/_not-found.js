"use strict";(()=>{var e={};e.id=165,e.ids=[165],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},2361:e=>{e.exports=require("events")},3685:e=>{e.exports=require("http")},5687:e=>{e.exports=require("https")},1808:e=>{e.exports=require("net")},5477:e=>{e.exports=require("punycode")},2781:e=>{e.exports=require("stream")},4404:e=>{e.exports=require("tls")},7310:e=>{e.exports=require("url")},9796:e=>{e.exports=require("zlib")},1383:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>x,originalPathname:()=>d,pages:()=>l,routeModule:()=>c,tree:()=>u});var s=t(7096),o=t(6132),n=t(7284),a=t.n(n),i=t(2564),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);t.d(r,p);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],l=[],d="/_not-found",x={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var r=require("../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[657,663,914],()=>__webpack_exec__(1383));module.exports=t})();