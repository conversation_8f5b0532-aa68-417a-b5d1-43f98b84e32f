(()=>{var e={};e.id=453,e.ids=[453],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},9074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>l});var s=r(7096),a=r(6132),n=r(7284),i=r.n(n),o=r(2564),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l=["",{children:["auth",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4659)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/auth/callback/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],u=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/auth/callback/page.tsx"],d="/auth/callback/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/callback/page",pathname:"/auth/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7375:(e,t,r)=>{Promise.resolve().then(r.bind(r,6983))},6983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>AuthCallbackPage});var s=r(784),a=r(9885),n=r(7114),i=r(8926);function AuthCallbackPage(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),[r,o]=(0,a.useState)("loading"),[c,l]=(0,a.useState)("Processing authentication...");return(0,a.useEffect)(()=>{let handleAuthCallback=async()=>{try{let r=new URLSearchParams(window.location.search),s=r.get("code");if(s){let{data:r,error:a}=await i.OQ.auth.exchangeCodeForSession(s);if(a){console.error("Code exchange error:",a),o("error"),l("Authentication failed. Please try again.");return}if(!r.session){o("error"),l("No session created. Please try again.");return}let n=r.session.user,c=t.get("type")||"login";if("signup"===c)o("success"),l("Please complete your restaurant setup..."),e.push("/setup");else{let{data:t,error:r}=await i.OQ.from("user_tenants").select(`
                tenant_id,
                role,
                tenants!inner(slug, name, status)
              `).eq("user_id",n.id).eq("is_active",!0).eq("tenants.status","ACTIVE");if(r){console.error("User tenant lookup error:",r),o("error"),l("Failed to load your account. Please try again.");return}if(!t||0===t.length){o("success"),l("Setting up your account..."),e.push("/setup");return}let s=t[0];o("success"),l("Redirecting to your dashboard..."),e.push(`/t/${s.tenants.slug}/dashboard`)}}else{let{data:t,error:r}=await i.OQ.auth.getSession();if(r||!t.session){o("error"),l("Authentication failed. Please try signing in again.");return}o("success"),l("Redirecting..."),e.push("/dashboard")}}catch(e){console.error("Auth callback error:",e),o("error"),l("An unexpected error occurred. Please try again.")}};handleAuthCallback()},[e,t]),s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:s.jsx("div",{className:"max-w-md w-full text-center",children:(0,s.jsxs)("div",{className:"mb-6",children:["loading"===r&&s.jsx("div",{className:"flex justify-center mb-4",children:(0,s.jsxs)("svg",{className:"animate-spin h-10 w-10 text-primary-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),"success"===r&&s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("svg",{className:"h-10 w-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),"error"===r&&s.jsx("div",{className:"flex justify-center mb-4",children:s.jsx("svg",{className:"h-10 w-10 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.982 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),s.jsx("p",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:c}),"error"===r&&(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[s.jsx("button",{onClick:()=>e.push("/login"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Back to Login"}),s.jsx("div",{className:"text-center",children:s.jsx("button",{onClick:()=>e.push("/signup"),className:"text-primary-600 hover:text-primary-500 text-sm font-medium",children:"Create a new account"})})]})]})})})}},4659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>c});var s=r(5153);let a=(0,s.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/auth/callback/page.tsx`),{__esModule:n,$$typeof:i}=a,o=a.default,c=o}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[657,663,914],()=>__webpack_exec__(9074));module.exports=r})();