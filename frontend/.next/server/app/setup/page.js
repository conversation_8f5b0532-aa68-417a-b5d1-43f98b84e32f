(()=>{var e={};e.id=413,e.ids=[413],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},2882:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var r=t(7096),a=t(6132),n=t(7284),i=t.n(n),l=t(2564),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let u=["",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4258)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/setup/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],d=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/setup/page.tsx"],c="/setup/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/setup/page",pathname:"/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},8740:(e,s,t)=>{Promise.resolve().then(t.bind(t,3440))},3440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>SetupPage});var r=t(784),a=t(9885),n=t(7114),i=t(8926),l=t(6860);function SetupPage(){let e=(0,n.useRouter)(),[s,t]=(0,a.useState)(null),[o,u]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),[m,p]=(0,a.useState)(null),[x,h]=(0,a.useState)({business_name:"",tenant_slug:"",business_type:"restaurant",phone:"",address:""}),{generateSlug:g,validateSlugAvailability:f,isValidSlugFormat:y}=(0,l.M)();(0,a.useEffect)(()=>{let checkAuth=async()=>{let{data:{session:s}}=await i.OQ.auth.getSession();if(!s){e.push("/login");return}if(t(s.user),s.user.user_metadata){let e=s.user.user_metadata;h(s=>({...s,business_name:e.full_name||e.name||""}))}u(!1)};checkAuth()},[e]),(0,a.useEffect)(()=>{if(x.business_name&&!x.tenant_slug){let e=g(x.business_name);h(s=>({...s,tenant_slug:e}))}},[x.business_name,g]);let handleChange=e=>{let{name:s,value:t}=e.target;h(e=>({...e,[s]:t}))},handleSubmit=async t=>{t.preventDefault(),c(!0),p(null);try{if(!y(x.tenant_slug))throw Error("Invalid tenant slug. Use only letters, numbers, and hyphens.");let t=await f(x.tenant_slug);if(!t)throw Error("This business name is already taken. Please choose another.");let{data:r,error:a}=await i.OQ.from("tenants").insert({name:x.business_name,slug:x.tenant_slug,email:s.email,phone:x.phone,address:x.address,status:"ACTIVE",settings:{business_type:x.business_type}}).select().single();if(console.log("Tenant creation result:",{tenant:r,tenantError:a}),a)throw a;let{data:n}=await i.OQ.from("users").select("id").eq("id",s.id).single();if(!n){let{error:e}=await i.OQ.from("users").insert({id:s.id,email:s.email,first_name:s.user_metadata?.first_name||s.user_metadata?.name||"",last_name:s.user_metadata?.last_name||""});e&&console.warn("User profile creation warning:",e)}let{error:l}=await i.OQ.from("user_tenants").insert({user_id:s.id,tenant_id:r.id,role:"OWNER",is_active:!0});if(l)throw l;e.push(`/t/${x.tenant_slug}/dashboard`)}catch(s){console.error("Setup error details:",s);let e="Setup failed. Please try again.";s instanceof Error?e=s.message:"object"==typeof s&&null!==s&&(s.message?e=s.message:s.details&&(e=`Database error: ${s.details}`)),p(e)}finally{c(!1)}};return o?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),r.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[r.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Complete Your Setup"}),r.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Let's set up your restaurant on TapDine"})]}),r.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:r.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,r.jsxs)("form",{className:"space-y-6",onSubmit:handleSubmit,children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"business_name",className:"block text-sm font-medium text-gray-700",children:"Business Name *"}),r.jsx("input",{id:"business_name",name:"business_name",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"Your Restaurant Name",value:x.business_name,onChange:handleChange})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"tenant_slug",className:"block text-sm font-medium text-gray-700",children:"URL Identifier *"}),(0,r.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[r.jsx("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm",children:"tapdine.com/"}),r.jsx("input",{id:"tenant_slug",name:"tenant_slug",type:"text",required:!0,className:"flex-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-none rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"your-restaurant",value:x.tenant_slug,onChange:e=>{let s=g(e.target.value);h(e=>({...e,tenant_slug:s}))}})]}),r.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"This will be your unique URL. Only letters, numbers, and hyphens allowed."})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"business_type",className:"block text-sm font-medium text-gray-700",children:"Business Type"}),(0,r.jsxs)("select",{id:"business_type",name:"business_type",className:"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",value:x.business_type,onChange:handleChange,children:[r.jsx("option",{value:"restaurant",children:"Restaurant"}),r.jsx("option",{value:"cafe",children:"Cafe"}),r.jsx("option",{value:"bar",children:"Bar"}),r.jsx("option",{value:"food_truck",children:"Food Truck"}),r.jsx("option",{value:"bakery",children:"Bakery"}),r.jsx("option",{value:"other",children:"Other"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),r.jsx("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"+****************",value:x.phone,onChange:handleChange})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address"}),r.jsx("input",{id:"address",name:"address",type:"text",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",placeholder:"123 Main St, City, State 12345",value:x.address,onChange:handleChange})]}),m&&r.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:r.jsx("p",{className:"text-sm text-red-700",children:m})}),r.jsx("div",{children:(0,r.jsxs)("button",{type:"submit",disabled:d,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[d?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):null,"Complete Setup"]})})]})})})]})}},4258:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var r=t(5153);let a=(0,r.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/setup/page.tsx`),{__esModule:n,$$typeof:i}=a,l=a.default,o=l}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[657,663,914,860],()=>__webpack_exec__(2882));module.exports=t})();