(()=>{var e={};e.id=421,e.ids=[421],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},6403:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(7096),a=t(6132),n=t(7284),o=t.n(n),i=t(2564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["[tenant]",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6705)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/orders/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,3770)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],c=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/orders/page.tsx"],u="/[tenant]/orders/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[tenant]/orders/page",pathname:"/[tenant]/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6886:(e,r,t)=>{Promise.resolve().then(t.bind(t,3816))},6705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>OrdersPage});var s=t(4656),a=t(2816);function OrdersPage(){return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(a.m,{title:"Orders",subtitle:"Track and manage your restaurant orders",breadcrumbs:[{label:"Dashboard",href:"/dashboard"},{label:"Orders"}],actions:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),"Filter Orders"]}),(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"New Order"]})]})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),s.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900 dark:text-white",children:"No Orders Yet"}),s.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Your restaurant orders will appear here once customers start placing orders."})]})})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[657,663,945,775,998,412,914,599,647,394,135],()=>__webpack_exec__(6403));module.exports=t})();