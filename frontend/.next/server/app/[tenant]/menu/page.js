(()=>{var e={};e.id=659,e.ids=[659],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},3334:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>l,routeModule:()=>x,tree:()=>u});var s=r(7096),n=r(6132),a=r(7284),o=r.n(a),i=r(2564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let u=["",{children:["[tenant]",{children:["menu",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1496)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/menu/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3770)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],l=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/menu/page.tsx"],c="/[tenant]/menu/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[tenant]/menu/page",pathname:"/[tenant]/menu",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6886:(e,t,r)=>{Promise.resolve().then(r.bind(r,3816))},1496:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>MenuPage});var s=r(4656),n=r(2816);function MenuPage(){return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(n.m,{title:"Menu Management",subtitle:"Manage your restaurant's menu items and categories",breadcrumbs:[{label:"Dashboard",href:"/dashboard"},{label:"Menu"}],actions:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Import Menu"]}),(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Menu Item"]})]})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),s.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900 dark:text-white",children:"Menu Management"}),s.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Start building your menu by adding categories and items."})]})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[657,663,945,775,998,412,914,599,647,394,135],()=>__webpack_exec__(3334));module.exports=r})();