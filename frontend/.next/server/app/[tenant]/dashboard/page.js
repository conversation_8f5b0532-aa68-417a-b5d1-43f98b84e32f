(()=>{var e={};e.id=678,e.ids=[678],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},2246:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>l,originalPathname:()=>c,pages:()=>u,routeModule:()=>x,tree:()=>p});var s=t(7096),a=t(6132),n=t(7284),o=t.n(n),i=t(2564),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p=["",{children:["[tenant]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2871)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,3770)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],u=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/dashboard/page.tsx"],c="/[tenant]/dashboard/page",l={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[tenant]/dashboard/page",pathname:"/[tenant]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},4087:(e,r,t)=>{Promise.resolve().then(t.bind(t,7521)),Promise.resolve().then(t.bind(t,3816))},2871:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>TenantDashboardPage});var s=t(4656),a=t(2816),n=t(7888);function TenantDashboardPage(){return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(a.m,{title:"Dashboard",subtitle:"Welcome to your restaurant management dashboard",actions:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Export Data"]}),(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"New Order"]})]})}),s.jsx(n.c,{})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[657,663,945,775,998,412,914,599,647,394,135,986],()=>__webpack_exec__(2246));module.exports=t})();