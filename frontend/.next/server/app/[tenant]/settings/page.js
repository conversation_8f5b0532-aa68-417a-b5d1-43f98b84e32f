(()=>{var e={};e.id=344,e.ids=[344],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},173:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var s=r(7096),n=r(6132),a=r(7284),i=r.n(a),o=r(2564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c=["",{children:["[tenant]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3801)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3770)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,9113)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,5666)),"/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/not-found.tsx"]}],l=["/Users/<USER>/Desktop/BHEEMDINE/frontend/src/app/[tenant]/settings/page.tsx"],u="/[tenant]/settings/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[tenant]/settings/page",pathname:"/[tenant]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6886:(e,t,r)=>{Promise.resolve().then(r.bind(r,3816))},3801:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>SettingsPage});var s=r(4656),n=r(2816);function SettingsPage(){return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(n.m,{title:"Settings",subtitle:"Configure your restaurant settings and preferences",breadcrumbs:[{label:"Dashboard",href:"/dashboard"},{label:"Settings"}],actions:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})}),"Export Settings"]}),(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700",children:[s.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"})}),"Save Changes"]})]})}),s.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),s.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900 dark:text-white",children:"Restaurant Settings"}),s.jsx("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Configure your restaurant's operational settings, branding, and preferences."})]})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[657,663,945,775,998,412,914,599,647,394,135],()=>__webpack_exec__(173));module.exports=r})();