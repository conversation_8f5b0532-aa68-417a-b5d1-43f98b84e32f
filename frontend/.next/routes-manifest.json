{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/[tenant]/dashboard", "regex": "^/([^/]+?)/dashboard(?:/)?$", "routeKeys": {"nxtPtenant": "nxtPtenant"}, "namedRegex": "^/(?<nxtPtenant>[^/]+?)/dashboard(?:/)?$"}, {"page": "/[tenant]/menu", "regex": "^/([^/]+?)/menu(?:/)?$", "routeKeys": {"nxtPtenant": "nxtPtenant"}, "namedRegex": "^/(?<nxtPtenant>[^/]+?)/menu(?:/)?$"}, {"page": "/[tenant]/orders", "regex": "^/([^/]+?)/orders(?:/)?$", "routeKeys": {"nxtPtenant": "nxtPtenant"}, "namedRegex": "^/(?<nxtPtenant>[^/]+?)/orders(?:/)?$"}, {"page": "/[tenant]/settings", "regex": "^/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPtenant": "nxtPtenant"}, "namedRegex": "^/(?<nxtPtenant>[^/]+?)/settings(?:/)?$"}, {"page": "/[tenant]/staff", "regex": "^/([^/]+?)/staff(?:/)?$", "routeKeys": {"nxtPtenant": "nxtPtenant"}, "namedRegex": "^/(?<nxtPtenant>[^/]+?)/staff(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/setup", "regex": "^/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}