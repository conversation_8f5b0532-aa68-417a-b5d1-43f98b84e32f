/**
 * Final Testing Script for BHEEMDINE Fixes
 * Tests signup functionality, styling, and tenant creation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const http = require('http');

console.log('🔧 Testing Final Fixes for BHEEMDINE...\n');

const testResults = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: { passed: 0, failed: 0, total: 0 }
};

function addTestResult(testName, status, message, details = null) {
  const result = { test: testName, status, message, details, timestamp: new Date().toISOString() };
  testResults.tests.push(result);
  testResults.summary.total++;
  
  if (status === 'PASS') {
    testResults.summary.passed++;
    console.log(`✅ ${testName}: ${message}`);
  } else {
    testResults.summary.failed++;
    console.log(`❌ ${testName}: ${message}`);
    if (details) console.log(`   Details: ${details}`);
  }
}

// Test 1: Tailwind Configuration Fix
console.log('1. Testing Tailwind Configuration...');
try {
  const tailwindConfig = fs.readFileSync('tailwind.config.js', 'utf8');
  
  // Check if the invalid color value is fixed
  const hasValidColor = tailwindConfig.includes('#006644') && !tailwindConfig.includes("'#064'");
  
  if (hasValidColor) {
    addTestResult('Tailwind Config', 'PASS', 'Primary color fixed with valid hex value');
  } else {
    addTestResult('Tailwind Config', 'FAIL', 'Primary color still has invalid hex value');
  }
} catch (error) {
  addTestResult('Tailwind Config', 'FAIL', 'Could not read Tailwind config', error.message);
}

// Test 2: CSS/Styling Working
console.log('2. Testing CSS and Styling...');
try {
  const globalCSS = fs.readFileSync('src/app/globals.css', 'utf8');
  
  // Check if Tailwind directives are present
  const hasDirectives = globalCSS.includes('@tailwind base') && 
                       globalCSS.includes('@tailwind components') && 
                       globalCSS.includes('@tailwind utilities');
  
  // Check if custom styles are defined
  const hasCustomStyles = globalCSS.includes('.btn-primary') && globalCSS.includes('.input-primary');
  
  if (hasDirectives && hasCustomStyles) {
    addTestResult('CSS/Styling', 'PASS', 'Tailwind directives and custom styles properly configured');
  } else {
    addTestResult('CSS/Styling', 'FAIL', 'CSS configuration incomplete');
  }
} catch (error) {
  addTestResult('CSS/Styling', 'FAIL', 'Could not read CSS files', error.message);
}

// Test 3: Signup API Route Fix
console.log('3. Testing Signup API Route...');
try {
  const signupRoute = fs.readFileSync('src/app/api/auth/signup/route.ts', 'utf8');
  
  // Check if the password parameter is included in generateLink
  const hasPasswordParam = signupRoute.includes('password: password,');
  const hasEnumValues = signupRoute.includes("'ACTIVE'") && signupRoute.includes("'OWNER'");
  
  if (hasPasswordParam && hasEnumValues) {
    addTestResult('Signup API Route', 'PASS', 'Signup route fixed with password parameter and correct enums');
  } else {
    addTestResult('Signup API Route', 'FAIL', 'Signup route still has issues');
  }
} catch (error) {
  addTestResult('Signup API Route', 'FAIL', 'Could not read signup route', error.message);
}

// Test 4: Tenant Creation Hook Fix
console.log('4. Testing Tenant Creation Hook...');
try {
  const useAuth = fs.readFileSync('src/hooks/useAuth.ts', 'utf8');
  
  // Check if email field is included in tenant creation
  const hasEmailField = useAuth.includes('email: signupData.email,');
  const hasUppercaseEnums = useAuth.includes("'ACTIVE'") && useAuth.includes("'OWNER'");
  
  if (hasEmailField && hasUppercaseEnums) {
    addTestResult('Tenant Creation Hook', 'PASS', 'useAuth hook fixed with email field and correct enums');
  } else {
    addTestResult('Tenant Creation Hook', 'FAIL', 'useAuth hook still has issues');
  }
} catch (error) {
  addTestResult('Tenant Creation Hook', 'FAIL', 'Could not read useAuth hook', error.message);
}

// Test 5: Build Process
console.log('5. Testing Build Process...');
try {
  execSync('npm run build', { stdio: 'pipe' });
  addTestResult('Build Process', 'PASS', 'Production build completed successfully');
} catch (error) {
  addTestResult('Build Process', 'FAIL', 'Build process failed', error.message);
}

// Test 6: Type System Integration
console.log('6. Testing Type System Integration...');
try {
  const authTypes = fs.readFileSync('src/types/auth.types.ts', 'utf8');
  const unifiedTypes = fs.readFileSync('../src/types/unified_database.types.ts', 'utf8');
  
  // Check if auth types re-export from unified types
  const hasReExport = authTypes.includes('from \'../../../src/types/unified_database.types\'');
  const hasUnifiedEnums = unifiedTypes.includes('ACTIVE') && unifiedTypes.includes('OWNER');
  
  if (hasReExport && hasUnifiedEnums) {
    addTestResult('Type System', 'PASS', 'Type system properly unified with correct enums');
  } else {
    addTestResult('Type System', 'FAIL', 'Type system integration incomplete');
  }
} catch (error) {
  addTestResult('Type System', 'FAIL', 'Could not read type files', error.message);
}

// Test 7: Database Schema Consistency
console.log('7. Testing Database Schema Consistency...');
try {
  const schema = fs.readFileSync('src/database/schema.sql', 'utf8');
  const rlsPolicies = fs.readFileSync('src/database/rls-policies.sql', 'utf8');
  
  // Check for uppercase enums in schema
  const schemaFixed = schema.includes("'ACTIVE'") && schema.includes("'OWNER'");
  const rlsFixed = rlsPolicies.includes("'OWNER'") && rlsPolicies.includes("'ADMIN'");
  
  if (schemaFixed && rlsFixed) {
    addTestResult('Database Schema', 'PASS', 'Database schema consistent with uppercase enums');
  } else {
    addTestResult('Database Schema', 'FAIL', 'Database schema still has lowercase enums');
  }
} catch (error) {
  addTestResult('Database Schema', 'FAIL', 'Could not read schema files', error.message);
}

// Test 8: Server Accessibility
console.log('8. Testing Server Accessibility...');
const testServer = (port) => {
  return new Promise((resolve) => {
    const request = http.get(`http://localhost:${port}`, (res) => {
      resolve(res.statusCode === 200);
    });
    request.on('error', () => resolve(false));
    request.setTimeout(5000, () => {
      request.destroy();
      resolve(false);
    });
  });
};

testServer(3000).then(isRunning => {
  if (isRunning) {
    addTestResult('Server Accessibility', 'PASS', 'Development server accessible on port 3000');
  } else {
    testServer(3001).then(isRunning3001 => {
      if (isRunning3001) {
        addTestResult('Server Accessibility', 'PASS', 'Development server accessible on port 3001');
      } else {
        addTestResult('Server Accessibility', 'FAIL', 'Development server not accessible');
      }
      printFinalResults();
    });
    return;
  }
  printFinalResults();
});

function printFinalResults() {
  console.log('\n📊 Final Test Results Summary:');
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`Passed: ${testResults.summary.passed}`);
  console.log(`Failed: ${testResults.summary.failed}`);
  console.log(`Success Rate: ${((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1)}%`);

  // Save detailed results
  fs.writeFileSync('final-test-results.json', JSON.stringify(testResults, null, 2));
  console.log('\n📄 Detailed results saved to final-test-results.json');

  // Final Status
  if (testResults.summary.failed === 0) {
    console.log('\n🎉 ALL FIXES WORKING! The application is ready for production.');
    console.log('✅ Signup functionality fixed');
    console.log('✅ Styling and colors working');
    console.log('✅ Tenant creation conflicts resolved');
    console.log('✅ Type system unified');
    console.log('✅ Database schema consistent');
    console.log('✅ Server accessible and functional');
  } else {
    console.log(`\n⚠️  ${testResults.summary.failed} issues still need attention.`);
  }
  
  console.log('\n🌐 Access the application at:');
  console.log('• http://localhost:3000');
  console.log('• http://localhost:3001 (if port 3000 is busy)');
}