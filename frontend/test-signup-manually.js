/**
 * Manual test script for signup functionality
 * This will help us debug the signup process step by step
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Environment check:');
console.log('SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
console.log('SERVICE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

// Create test data
const testData = {
  businessName: 'Test Restaurant',
  tenantSlug: 'test-restaurant-' + Date.now(),
  adminEmail: 'test' + Date.now() + '@example.com',
  password: 'TestPassword123!'
};

console.log('\nTesting signup with:', { ...testData, password: '[REDACTED]' });

async function testSignup() {
  try {
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    console.log('\n1. Checking if tenant slug exists...');
    const { data: existingTenant } = await supabase
      .from('tenants')
      .select('slug')
      .eq('slug', testData.tenantSlug)
      .single();

    if (existingTenant) {
      console.log('❌ Tenant slug already exists');
      return;
    }
    console.log('✅ Tenant slug is available');
    
    console.log('\n2. Creating auth user...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testData.adminEmail,
      password: testData.password,
      email_confirm: true,
    });

    if (authError || !authData.user) {
      console.error('❌ Auth user creation failed:', authError);
      return;
    }
    console.log('✅ Auth user created:', authData.user.id);
    
    console.log('\n3. Creating tenant...');
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        id: crypto.randomUUID(),
        name: testData.businessName,
        slug: testData.tenantSlug,
        email: testData.adminEmail,
        status: 'ACTIVE',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (tenantError || !tenant) {
      console.error('❌ Tenant creation failed:', tenantError);
      // Clean up auth user
      await supabase.auth.admin.deleteUser(authData.user.id);
      return;
    }
    console.log('✅ Tenant created:', tenant.id);
    
    console.log('\n4. Creating user profile...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: testData.adminEmail,
        first_name: testData.businessName,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (userError || !user) {
      console.error('❌ User profile creation failed:', userError);
      // Clean up
      await supabase.auth.admin.deleteUser(authData.user.id);
      await supabase.from('tenants').delete().eq('id', tenant.id);
      return;
    }
    console.log('✅ User profile created:', user.id);
    
    console.log('\n5. Creating user-tenant association...');
    const { error: associationError } = await supabase
      .from('user_tenants')
      .insert({
        id: crypto.randomUUID(),
        user_id: authData.user.id,
        tenant_id: tenant.id,
        role: 'OWNER',
        is_active: true,
      });

    if (associationError) {
      console.error('❌ User-tenant association failed:', associationError);
      // Clean up all records
      await supabase.auth.admin.deleteUser(authData.user.id);
      await supabase.from('tenants').delete().eq('id', tenant.id);
      await supabase.from('users').delete().eq('id', user.id);
      return;
    }
    console.log('✅ User-tenant association created');
    
    console.log('\n🎉 SIGNUP TEST SUCCESSFUL!');
    console.log('All components working correctly:');
    console.log('- Auth user creation: ✅');
    console.log('- Tenant creation: ✅');
    console.log('- User profile creation: ✅');
    console.log('- User-tenant association: ✅');
    
    // Clean up test data
    console.log('\nCleaning up test data...');
    await supabase.from('user_tenants').delete().eq('user_id', authData.user.id);
    await supabase.from('users').delete().eq('id', user.id);
    await supabase.from('tenants').delete().eq('id', tenant.id);
    await supabase.auth.admin.deleteUser(authData.user.id);
    console.log('✅ Cleanup complete');
    
  } catch (error) {
    console.error('❌ Signup test failed:', error);
  }
}

// Run the test
testSignup();