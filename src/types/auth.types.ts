/**
 * Multi-tenant authentication types for BHEEMDINE
 * Re-exports from unified database types
 */

import { 
  Tenant as DatabaseTenant,
  User as DatabaseUser,
  TenantStatus,
  UserRole
} from '../../../src/types/unified_database.types';

// Re-export database types for backward compatibility
export type Tenant = DatabaseTenant;
export type User = DatabaseUser;
export type { TenantStatus, UserRole };

export interface AuthSession {
  user: User;
  tenant: Tenant;
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
}

export interface TenantContext {
  tenant: Tenant | null;
  user: User | null;
  isLoading: boolean;
  error: Error | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  tenant_slug?: string;
}

export interface SignupData extends LoginCredentials {
  tenant_name: string;
  first_name?: string;
  last_name?: string;
}