/**
 * Authentication hooks for multi-tenant system
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';
import { useTenant } from '@/contexts/TenantContext';
import { LoginCredentials, SignupData } from '@/types/auth.types';

export function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { tenant, user } = useTenant();

  /**
   * Sign in with email and password
   */
  const signIn = async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw error;

      // Verify tenant access if tenant_slug is provided
      if (credentials.tenant_slug) {
        const hasAccess = await verifyTenantAccess(data.user.id, credentials.tenant_slug);
        if (!hasAccess) {
          throw new Error('Access denied to this tenant');
        }
      }

      // Redirect to dashboard or intended page
      const returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/dashboard';
      router.push(returnUrl);
      
      return { user: data.user, session: data.session };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign up new user and create tenant
   */
  const signUp = async (signupData: SignupData) => {
    try {
      setLoading(true);
      setError(null);

      // Create user account
      const { data, error } = await supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
        options: {
          data: {
            first_name: signupData.first_name,
            last_name: signupData.last_name,
          },
        },
      });

      if (error) throw error;

      // Create tenant if new signup (not existing tenant)
      if (data.user && !signupData.tenant_slug) {
        await createTenant(data.user.id, signupData);
      }

      return { user: data.user, session: data.session };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign out current user
   */
  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      router.push('/login');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset password
   */
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) throw error;
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update password
   */
  const updatePassword = async (newPassword: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    tenant,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };
}

/**
 * Verify user has access to tenant
 */
async function verifyTenantAccess(userId: string, tenantSlug: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('user_tenants')
      .select('tenant_id, tenants!inner(slug, status)')
      .eq('user_id', userId)
      .eq('tenants.slug', tenantSlug)
      .eq('tenants.status', 'ACTIVE')
      .single();

    return !error && !!data;
  } catch {
    return false;
  }
}

/**
 * Create new tenant for user
 */
async function createTenant(userId: string, signupData: SignupData) {
  try {
    // Generate tenant slug from name
    const tenantSlug = signupData.tenant_name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    // Create tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        name: signupData.tenant_name,
        slug: tenantSlug,
        email: signupData.email,
        status: 'ACTIVE' as const,
      })
      .select()
      .single();

    if (tenantError) throw tenantError;

    // Create user profile
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: signupData.email,
        first_name: signupData.first_name,
        last_name: signupData.last_name,
      });

    if (userError) throw userError;

    // Associate user with tenant
    const { error: associationError } = await supabase
      .from('user_tenants')
      .insert({
        user_id: userId,
        tenant_id: tenant.id,
        role: 'OWNER' as const,
      });

    if (associationError) throw associationError;

    return tenant;
  } catch (error) {
    console.error('Error creating tenant:', error);
    throw error;
  }
}