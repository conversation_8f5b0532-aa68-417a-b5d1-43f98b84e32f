/**
 * Hook for generating and validating URL slugs
 * Converts business names to URL-friendly slugs
 */

'use client';

import { useState, useCallback } from 'react';

export function useSlugify() {
  const [isValidating, setIsValidating] = useState(false);

  // Generate slug from business name
  const generateSlug = useCallback((businessName: string): string => {
    return businessName
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }, []);

  // Validate slug availability (simulated API call)
  const validateSlugAvailability = useCallback(async (slug: string): Promise<boolean> => {
    if (!slug || slug.length < 3) return false;
    
    setIsValidating(true);
    
    try {
      // Simulate API call to check slug availability
      const response = await fetch(`/api/auth/validate-slug?slug=${encodeURIComponent(slug)}`);
      const data = await response.json();
      return data.available;
    } catch (error) {
      console.error('Slug validation error:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Check if slug is valid format
  const isValidSlugFormat = useCallback((slug: string): boolean => {
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 50;
  }, []);

  // Reserved slugs that cannot be used
  const reservedSlugs = [
    'api', 'admin', 'app', 'www', 'mail', 'ftp', 'localhost',
    'dashboard', 'billing', 'support', 'help', 'docs', 'blog',
    'status', 'staging', 'dev', 'test', 'demo', 'cdn', 'assets',
    'static', 'public', 'private', 'secure', 'internal', 'system',
    'root', 'user', 'account', 'login', 'signup', 'auth', 'oauth',
    'settings', 'profile', 'home', 'index', 'about', 'contact',
    'terms', 'privacy', 'legal', 'pricing', 'features', 'enterprise'
  ];

  const isReservedSlug = useCallback((slug: string): boolean => {
    return reservedSlugs.includes(slug.toLowerCase());
  }, []);

  return {
    generateSlug,
    validateSlugAvailability,
    isValidSlugFormat,
    isReservedSlug,
    isValidating,
  };
}