/**
 * Mobile Overlay Component
 * Provides backdrop overlay for mobile sidebar
 */

'use client';

import React from 'react';

interface MobileOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileOverlay({ isOpen, onClose }: MobileOverlayProps) {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity duration-300 ease-linear lg:hidden"
      onClick={onClose}
      aria-hidden="true"
    />
  );
}