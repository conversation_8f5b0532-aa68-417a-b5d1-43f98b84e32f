/**
 * Main Dashboard Layout Component for TapDine B2B Multi-Tenant SaaS
 * Provides responsive navigation, sidebar, and main content area
 */

'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { TopNavigation } from './TopNavigation';
import { Sidebar } from './Sidebar';
import { MobileOverlay } from './MobileOverlay';
import { useTenant } from '@/contexts/TenantContext';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const pathname = usePathname();
  const { tenant } = useTenant();

  // Initialize dark mode from localStorage
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode') === 'true';
    setDarkMode(savedDarkMode);
    
    // Apply dark mode class to document
    if (savedDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, []);

  // Close sidebar when route changes (mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Handle dark mode toggle
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // Close sidebar when clicking outside (mobile)
  const handleOverlayClick = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar overlay */}
      <MobileOverlay 
        isOpen={sidebarOpen} 
        onClose={handleOverlayClick}
      />

      {/* Desktop sidebar */}
      <Sidebar 
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        tenant={tenant}
      />

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
        {/* Top navigation */}
        <TopNavigation
          onMenuClick={() => setSidebarOpen(true)}
          darkMode={darkMode}
          onDarkModeToggle={toggleDarkMode}
          tenant={tenant}
        />

        {/* Page content */}
        <main 
          className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 lg:p-6"
          role="main"
          aria-label="Main content"
        >
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}