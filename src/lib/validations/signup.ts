/**
 * Zod validation schemas for tenant admin signup
 */

import { z } from 'zod';

// Business name validation
export const businessNameSchema = z
  .string()
  .min(2, 'Business name must be at least 2 characters')
  .max(100, 'Business name cannot exceed 100 characters')
  .regex(/^[a-zA-Z0-9\s\-'&.]+$/, 'Business name contains invalid characters')
  .transform(val => val.trim());

// Tenant slug validation
export const tenantSlugSchema = z
  .string()
  .min(3, 'Tenant slug must be at least 3 characters')
  .max(50, 'Tenant slug cannot exceed 50 characters')
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
  .refine(val => !val.startsWith('-') && !val.endsWith('-'), 'Slug cannot start or end with hyphen')
  .refine(val => !val.includes('--'), 'Slug cannot contain consecutive hyphens')
  .transform(val => val.toLowerCase().trim());

// Email validation
export const emailSchema = z
  .string()
  .email('Please enter a valid email address')
  .max(255, 'Email cannot exceed 255 characters')
  .transform(val => val.toLowerCase().trim());

// Password validation
export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password cannot exceed 128 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character');

// Complete signup form schema
export const signupFormSchema = z.object({
  businessName: businessNameSchema,
  tenantSlug: tenantSlugSchema,
  adminEmail: emailSchema,
  password: passwordSchema,
});

export type SignupFormData = z.infer<typeof signupFormSchema>;

// API response schemas
export const signupResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    tenant: z.object({
      id: z.string(),
      name: z.string(),
      slug: z.string(),
    }),
    user: z.object({
      id: z.string(),
      email: z.string(),
    }),
    session: z.object({
      access_token: z.string(),
      refresh_token: z.string(),
      expires_in: z.number(),
    }),
  }).optional(),
});

export const signupErrorSchema = z.object({
  success: z.boolean().default(false),
  error: z.string(),
  details: z.record(z.array(z.string())).optional(),
});

export type SignupResponse = z.infer<typeof signupResponseSchema>;
export type SignupError = z.infer<typeof signupErrorSchema>;