/**
 * Supabase client configuration with multi-tenant support
 */

import { createBrowserClient } from '@supabase/ssr';
import { Database } from '@/types/database.types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Verify configuration (only in development)
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Supabase configuration missing');
  }
}

export const createClient = () =>
  createBrowserClient<Database>(
    supabaseUrl!,
    supabaseKey!,
  );

// Legacy function for backward compatibility
export function getSupabaseClient() {
  return createClient();
}

// Helper to get tenant-scoped Supabase client
export function getTenantSupabaseClient(tenantId: string) {
  const client = getSupabaseClient();
  
  // Add tenant context to all queries
  client.auth.onAuthStateChange((event, session) => {
    if (session) {
      // Set tenant context in headers for RLS
      // Note: This is a workaround for tenant isolation
      // RLS policies should handle tenant filtering instead
      localStorage.setItem('x-tenant-id', tenantId);
    }
  });

  return client;
}

// Export the default client
export const supabase = getSupabaseClient();