/**
 * Type guards for runtime validation of critical types
 */

import { TenantStatus, UserRole } from '@/types/database.types';

// Enum validation constants
export const VALID_TENANT_STATUSES: TenantStatus[] = ['ACTIVE', 'SUSPENDED', 'INACTIVE', 'PENDING'];
export const VALID_USER_ROLES: UserRole[] = ['OWNER', 'ADMIN', 'MANAGER', 'STAFF', 'READONLY'];

/**
 * Type guard for TenantStatus
 */
export function isTenantStatus(value: unknown): value is TenantStatus {
  return typeof value === 'string' && VALID_TENANT_STATUSES.includes(value as TenantStatus);
}

/**
 * Type guard for UserRole
 */
export function isUserRole(value: unknown): value is UserRole {
  return typeof value === 'string' && VALID_USER_ROLES.includes(value as UserRole);
}

/**
 * Type guard for valid tenant data
 */
export function isValidTenant(tenant: unknown): tenant is { id: string; name: string; slug: string; status: TenantStatus } {
  if (!tenant || typeof tenant !== 'object') return false;
  
  const t = tenant as any;
  return (
    typeof t.id === 'string' &&
    typeof t.name === 'string' &&
    typeof t.slug === 'string' &&
    isTenantStatus(t.status)
  );
}

/**
 * Type guard for valid user data
 */
export function isValidUser(user: unknown): user is { id: string; email: string; role?: UserRole } {
  if (!user || typeof user !== 'object') return false;
  
  const u = user as any;
  return (
    typeof u.id === 'string' &&
    typeof u.email === 'string' &&
    (u.role === undefined || isUserRole(u.role))
  );
}

/**
 * Validates and normalizes enum values from database
 */
export function normalizeTenantStatus(value: unknown): TenantStatus {
  if (typeof value === 'string') {
    const normalized = value.toUpperCase() as TenantStatus;
    if (isTenantStatus(normalized)) {
      return normalized;
    }
  }
  
  console.warn('Invalid tenant status received:', value, 'defaulting to ACTIVE');
  return 'ACTIVE';
}

/**
 * Validates and normalizes user role from database
 */
export function normalizeUserRole(value: unknown): UserRole {
  if (typeof value === 'string') {
    const normalized = value.toUpperCase() as UserRole;
    if (isUserRole(normalized)) {
      return normalized;
    }
  }
  
  console.warn('Invalid user role received:', value, 'defaulting to STAFF');
  return 'STAFF';
}

/**
 * Safe enum converter that handles both uppercase and lowercase inputs
 */
export function safeConvertTenantStatus(value: string): TenantStatus {
  const upperValue = value.toUpperCase();
  
  // Handle common conversions
  switch (upperValue) {
    case 'ACTIVE':
      return 'ACTIVE';
    case 'SUSPENDED':
      return 'SUSPENDED';
    case 'INACTIVE':
      return 'INACTIVE';
    case 'PENDING':
      return 'PENDING';
    default:
      console.warn(`Unknown tenant status: ${value}, defaulting to ACTIVE`);
      return 'ACTIVE';
  }
}

/**
 * Safe enum converter for user roles
 */
export function safeConvertUserRole(value: string): UserRole {
  const upperValue = value.toUpperCase();
  
  // Handle common conversions
  switch (upperValue) {
    case 'OWNER':
      return 'OWNER';
    case 'ADMIN':
      return 'ADMIN';
    case 'MANAGER':
      return 'MANAGER';
    case 'STAFF':
      return 'STAFF';
    case 'READONLY':
      return 'READONLY';
    default:
      console.warn(`Unknown user role: ${value}, defaulting to STAFF`);
      return 'STAFF';
  }
}