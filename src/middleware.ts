/**
 * Next.js middleware for multi-tenant authentication and routing
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/middleware';

// Define route types
const PUBLIC_ROUTES = ['/', '/login', '/signup', '/forgot-password'];
const AUTH_ROUTES = ['/login', '/signup'];
const TENANT_ROUTES = ['/dashboard', '/menu', '/orders', '/settings'];

export async function middleware(request: NextRequest) {
  // Create Supabase client with cookies
  const { supabase, response } = createClient(request);

  // Get current session
  const { data: { session } } = await supabase.auth.getSession();
  
  const pathname = request.nextUrl.pathname;
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);
  const isAuthRoute = AUTH_ROUTES.includes(pathname);
  const isTenantRoute = TENANT_ROUTES.some(route => pathname.startsWith(route));

  // Extract tenant slug from URL or subdomain
  const tenantSlug = extractTenantSlug(request);

  // Add tenant context to headers
  if (tenantSlug) {
    response.headers.set('x-tenant-slug', tenantSlug);
  }

  // Handle authentication logic
  if (!session) {
    // User is not authenticated
    if (isTenantRoute) {
      // Redirect to login with return URL
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('returnUrl', pathname);
      if (tenantSlug) {
        redirectUrl.searchParams.set('tenant', tenantSlug);
      }
      return NextResponse.redirect(redirectUrl);
    }
  } else {
    // User is authenticated
    if (isAuthRoute) {
      // Redirect authenticated users away from auth pages
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    if (isTenantRoute) {
      // Verify tenant access
      const hasAccess = await verifyTenantAccess(supabase, session.user.id, tenantSlug);
      
      if (!hasAccess) {
        // User doesn't have access to this tenant
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }

      // Set tenant context in cookies for client-side access
      response.cookies.set('tenant-slug', tenantSlug || '', {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });
    }
  }

  return response;
}

/**
 * Extract tenant slug from request
 * Supports both subdomain and path-based multi-tenancy
 */
function extractTenantSlug(request: NextRequest): string | null {
  const url = new URL(request.url);
  
  // 1. Check subdomain (e.g., acme.bheemdine.com)
  const hostname = request.headers.get('host') || '';
  const subdomain = hostname.split('.')[0];
  if (subdomain && subdomain !== 'www' && subdomain !== 'bheemdine') {
    return subdomain;
  }

  // 2. Check URL path (e.g., /t/acme/dashboard)
  const pathMatch = request.nextUrl.pathname.match(/^\/t\/([^\/]+)/);
  if (pathMatch) {
    return pathMatch[1];
  }

  // 3. Check query parameter (e.g., ?tenant=acme)
  const tenantParam = request.nextUrl.searchParams.get('tenant');
  if (tenantParam) {
    return tenantParam;
  }

  // 4. Check cookie (for persistent tenant context)
  const tenantCookie = request.cookies.get('tenant-slug');
  if (tenantCookie) {
    return tenantCookie.value;
  }

  return null;
}

/**
 * Verify user has access to the specified tenant
 */
async function verifyTenantAccess(
  supabase: any,
  userId: string,
  tenantSlug: string | null
): Promise<boolean> {
  if (!tenantSlug) return false;

  try {
    // Query the user's tenant associations
    const { data, error } = await supabase
      .from('user_tenants')
      .select('tenant_id, role, tenants!inner(slug, status)')
      .eq('user_id', userId)
      .eq('tenants.slug', tenantSlug)
      .eq('tenants.status', 'ACTIVE')
      .single();

    if (error || !data) {
      console.error('Tenant access verification failed:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error verifying tenant access:', error);
    return false;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public directory)
     * - api routes (handled separately)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$|api).*)',
  ],
};