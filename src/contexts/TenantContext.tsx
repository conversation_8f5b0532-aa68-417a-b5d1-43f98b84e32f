/**
 * Tenant context provider for multi-tenant authentication
 */

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@supabase/supabase-js';
import { Tenant, User as AppUser, TenantContext as TenantContextType } from '@/types/auth.types';
import { supabase } from '@/lib/supabase/client';
import { normalizeTenantStatus, normalizeUserRole, isValidTenant, isValidUser } from '@/lib/type-guards';

interface TenantProviderProps {
  children: React.ReactNode;
  initialTenant?: Tenant | null;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children, initialTenant = null }: TenantProviderProps) {
  const [tenant, setTenant] = useState<Tenant | null>(initialTenant);
  const [user, setUser] = useState<AppUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const router = useRouter();

  useEffect(() => {
    let mounted = true;

    async function loadTenantAndUser() {
      try {
        setIsLoading(true);
        setError(null);

        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) throw sessionError;
        
        if (!session) {
          if (mounted) {
            setUser(null);
            setTenant(null);
            setIsLoading(false);
          }
          return;
        }

        // Get tenant slug from various sources
        const tenantSlug = getTenantSlug();
        
        if (!tenantSlug && !initialTenant) {
          // No tenant context available
          if (mounted) {
            setError(new Error('No tenant context available'));
            setIsLoading(false);
          }
          return;
        }

        // Fetch user's tenant data
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select(`
            *,
            user_tenants!inner(
              role,
              tenant:tenants(*)
            )
          `)
          .eq('id', session.user.id)
          .single();

        if (userError) throw userError;

        // Find the matching tenant
        const userTenant = userData.user_tenants.find(
          (ut: any) => ut.tenant.slug === (tenantSlug || initialTenant?.slug)
        );

        if (!userTenant) {
          throw new Error('User does not have access to this tenant');
        }

        if (mounted) {
          // Validate and normalize tenant data
          const tenantData = {
            ...userTenant.tenant,
            status: normalizeTenantStatus(userTenant.tenant.status)
          };
          
          // Validate and normalize user data
          const userDataNormalized = {
            id: userData.id,
            email: userData.email,
            tenant_id: userTenant.tenant.id,
            role: normalizeUserRole(userTenant.role),
            first_name: userData.first_name,
            last_name: userData.last_name,
            avatar_url: userData.avatar_url,
            created_at: userData.created_at,
            updated_at: userData.updated_at,
          };
          
          // Validate data before setting state
          if (isValidTenant(tenantData) && isValidUser(userDataNormalized)) {
            setUser(userDataNormalized);
            setTenant(tenantData);
          } else {
            throw new Error('Invalid tenant or user data received');
          }
          
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Error loading tenant context:', err);
        if (mounted) {
          setError(err as Error);
          setIsLoading(false);
        }
      }
    }

    loadTenantAndUser();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          loadTenantAndUser();
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setTenant(null);
          router.push('/login');
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [router, initialTenant]);

  return (
    <TenantContext.Provider value={{ tenant, user, isLoading, error }}>
      {children}
    </TenantContext.Provider>
  );
}

/**
 * Hook to use tenant context
 */
export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

/**
 * Get tenant slug from various sources
 */
function getTenantSlug(): string | null {
  if (typeof window === 'undefined') return null;

  // 1. Check subdomain
  const hostname = window.location.hostname;
  const subdomain = hostname.split('.')[0];
  if (subdomain && subdomain !== 'www' && subdomain !== 'localhost') {
    return subdomain;
  }

  // 2. Check URL path
  const pathMatch = window.location.pathname.match(/^\/t\/([^\/]+)/);
  if (pathMatch) {
    return pathMatch[1];
  }

  // 3. Check query parameter
  const params = new URLSearchParams(window.location.search);
  const tenantParam = params.get('tenant');
  if (tenantParam) {
    return tenantParam;
  }

  // 4. Check cookie
  const tenantCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('tenant-slug='));
  if (tenantCookie) {
    return tenantCookie.split('=')[1];
  }

  return null;
}